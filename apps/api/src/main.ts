import * as fs from 'fs';
import { join } from 'path';

import { ValidationPipe, INestApplication } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import cookieParser from 'cookie-parser';
import * as express from 'express';
import { Request, Response, NextFunction } from 'express';
import basicAuth from 'express-basic-auth';
import helmet from 'helmet';
import { SpelunkerModule } from 'nestjs-spelunker';

import { AppModule } from './app.module';

// Extend the Express Request interface to include rawBody
declare module 'express' {
  interface Request {
    rawBody?: Buffer;
  }
}

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    bodyParser: true,
    rawBody: true,
  });

  app.use(
    express.json({
      limit: '10mb',
      verify: (req: Request, res: Response, buf: Buffer) => {
        if (buf && buf.length) {
          req.rawBody = buf;
        }
      },
    }),
  );
  app.use(express.urlencoded({ limit: '10mb', extended: true }));

  app.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: [
            "'self'",
            "'unsafe-inline'",
            'https://fonts.googleapis.com',
          ],
          fontSrc: ["'self'", 'https://fonts.gstatic.com'],
          imgSrc: ["'self'", 'data:', 'https:', 'blob:'],
          scriptSrc: ["'self'"],
          connectSrc: ["'self'"],
        },
      },
      crossOriginEmbedderPolicy: false,
    }),
  );

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      transformOptions: { enableImplicitConversion: true },
    }),
  );

  if (
    process.env.CI_COMMIT_REF_NAME === 'dev' &&
    process.env.BASIC_AUTH_PASSWORD
  ) {
    const basicAuthOptions = {
      users: {
        cv: process.env.BASIC_AUTH_PASSWORD,
      },
      challenge: true,
      unauthorizedResponse: () => 'Unauthorized',
    };

    const exemptPaths: string[] = ['/api/health'];

    const conditionalBasicAuth: express.RequestHandler = (req, res, next) => {
      const path = req.url.split('?')[0];
      if (exemptPaths.some((p) => path.startsWith(p))) {
        return next();
      }
      return basicAuth(basicAuthOptions)(req, res, next);
    };

    app.use(conditionalBasicAuth);
  }

  const globalPrefix = 'api';
  if (process.env.NODE_ENV === 'development') {
    app.enableCors({
      origin: process.env.VITE_WEB_BASE_URL,
      credentials: true,
    });
    void generateDependencyGraph(app);
  }
  app.setGlobalPrefix(globalPrefix);
  app.use(cookieParser());

  if (process.env.NODE_ENV === 'production') {
    const frontendPath = join(__dirname, '..', 'web');
    app.use(express.static(frontendPath));

    app.use((req: Request, res: Response, next: NextFunction) => {
      if (!req.path.startsWith(`/${globalPrefix}`)) {
        res.sendFile(join(frontendPath, 'index.html'));
      } else {
        next();
      }
    });
  }

  const port = process.env.PORT || 3333;
  await app.listen(port);
  console.log(`🚀 Application is running on port ${port}`);
}

bootstrap();

async function generateDependencyGraph(app: INestApplication) {
  // Module dependencies graph
  const tree = SpelunkerModule.explore(app);
  const root = SpelunkerModule.graph(tree);
  const edges = SpelunkerModule.findGraphEdges(root);
  const mermaidEdges = edges
    .map(({ from, to }) => `  ${from.module.name}-->${to.module.name}`)
    // filter out modules from the chart if you need
    .filter(
      (edge) =>
        !edge.includes('FilteredModule') && !edge.includes('OtherExample'),
    )
    .sort();
  // write into file
  fs.writeFileSync(
    'deps.mermaid',
    `graph LR
${mermaidEdges.join('\n')}`,
  );
}
