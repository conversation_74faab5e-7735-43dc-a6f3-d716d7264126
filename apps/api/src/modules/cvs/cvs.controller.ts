import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { CreateCvDto, RegenerateCvDto } from 'shared/dto';

import { CvsService } from './cvs.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { AuthUser } from '../global/decorators/user.decorator';
import { AuthUserDto } from '../global/dto/auth-user.dto';
import { CheckPlanLimit } from '../stripe/decorators/check-plan-limit.decorator';
import { LimitType } from '../stripe/guards/plan-limit.guard';
import { PlanLimitGuard } from '../stripe/guards/plan-limit.guard';

@UseGuards(AuthGuard)
@Controller('cvs')
export class CvsController {
  constructor(private cvsService: CvsService) {}

  @UseGuards(PlanLimitGuard)
  @CheckPlanLimit(LimitType.CVS) // Apply decorator
  @Post('create/:memberId')
  async createCv(
    @AuthUser() user: AuthUserDto,
    @Param('memberId') memberId: string,
    @Body() dto: CreateCvDto,
  ) {
    const newCv = await this.cvsService.createCv(
      memberId,
      user.organization._id,
      dto,
    );

    return newCv;
  }

  @Get('byMember/:memberId')
  getMemberCvs(
    @AuthUser() user: AuthUserDto,
    @Param('memberId') memberId: string,
  ) {
    return this.cvsService.getMemberCvs(memberId);
  }

  @Post('regenerate/:id')
  regenerateCv(@Param('id') id: string, @Body() dto: RegenerateCvDto) {
    return this.cvsService.regenerateCv(id, dto.query);
  }
}
