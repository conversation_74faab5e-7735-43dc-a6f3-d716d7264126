import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CreateCvDto } from 'shared/dto';

import { ICv, CvModel } from './cv.schema';
import { Member, MemberDocument } from '../members/member.schema';

@Injectable()
export class CvsService {
  constructor(
    @InjectModel(CvModel.modelName) private cvModel: Model<ICv>,
    @InjectModel(Member.name) private memberModel: Model<MemberDocument>,
  ) {}

  async regenerateCv(cvId: string, query: string) {
    console.log(cvId, query);
    return 1;
  }

  async getMemberCvs(memberId: string) {
    return await this.cvModel.find({
      member: memberId,
    });
  }

  async createCv(
    memberId: string,
    organizationId: Types.ObjectId,
    dto: CreateCvDto,
  ) {
    const newCv = await this.cvModel.create({
      member: memberId,
      organization: organizationId,
      preferences: dto,
      sections: {
        personalInfo: {
          order: 1,
          title: 'Personal Information',
          data: {
            firstName: '',
            lastName: '',
            jobTitle: { value: '', active: true },
            location: { value: '', active: true },
            nationality: { value: '', active: true },
            email: { value: '', active: true },
            telephone: { value: '', active: true },
            socials: [],
          },
        },
        aboutMe: {
          order: 2,
          title: 'About Me',
          data: {
            aboutMe: '',
          },
        },
        workHistory: {
          order: 3,
          title: 'Work History',
          data: [],
        },
        education: {
          order: 4,
          title: 'Education',
          data: [],
        },
        certifications: {
          order: 5,
          title: 'Certifications',
          data: {
            certifications: '',
          },
        },
        skills: {
          order: 6,
          title: 'Skills',
          data: {
            skills: '',
          },
        },
        languages: {
          order: 7,
          title: 'Languages',
          data: {
            languages: '',
          },
        },
        customSections: [],
      },
    });

    await this.memberModel.updateOne(
      { _id: memberId },
      {
        $push: { cvs: newCv.id },
      },
    );

    return newCv;
  }

  async deleteOrganizationCvs(organizationId: string | Types.ObjectId) {
    await this.cvModel.deleteMany({ organization: organizationId });
  }
}
