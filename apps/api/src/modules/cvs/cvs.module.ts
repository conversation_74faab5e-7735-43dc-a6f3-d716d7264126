import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { CvModel, CvSchema } from './cv.schema';
import { CvsController } from './cvs.controller';
import { CvsService } from './cvs.service';
import { Member, MemberSchema } from '../members/member.schema';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: CvModel.modelName, schema: CvSchema }]),
    MongooseModule.forFeature([{ name: Member.name, schema: MemberSchema }]),
  ],
  controllers: [CvsController],
  providers: [CvsService],
  exports: [CvsService],
})
export class CvsModule {}
