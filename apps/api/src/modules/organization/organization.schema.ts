import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, Types } from 'mongoose';
import { StripePlanMapping } from 'shared/types';

import { BaseModel } from '../../db';
import {
  ICertification,
  IEducationRecord,
  ISkill,
  IWorkRecord,
} from '../members/member.schema';

export interface IOrganization extends BaseModel {
  name: string;
  invites: Types.ObjectId[];
  photo?: string;
  cvs: Types.ObjectId[];
  locations: string[];
  description: string;
  website: string;
  timezone: string;
  muchskillsMembers?: IMuchskillsMember[];
  muchskillsIntegration?: IMuchskillsIntegration;

  // Stripe related fields
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  planStatus?:
    | 'trialing'
    | 'active'
    | 'past_due'
    | 'canceled'
    | 'unpaid'
    | 'incomplete'
    | 'incomplete_expired'
    | 'paused'
    | 'free';
  currentPeriodEnds?: Date;
  planId?: string;
  planTier?: StripePlanMapping;
}

// ... (rest of the interfaces IMuchskillsIntegration, IMuchskillsMember)
export interface IMuchskillsIntegration {
  token: string;
  connected: boolean;
  lastSync?: Date;
}

export interface IMuchskillsMember extends BaseModel {
  profile: {
    //There should be an msId to match CVB members with MS ones
    email: string;
    name: string;
    image: string;
    title: string;
    about: string;
    department: string;
    location: string;
    links: string[];
    references: string[];
    tags: string[];
    workExperience: IWorkRecord[];
    education: IEducationRecord[];
  };
  skills: ISkill[];
  certifications: ICertification[];
}

export type OrganizationDocument = IOrganization & Document;

@Schema({ timestamps: true })
export class Organization {
  _id: Types.ObjectId;

  @Prop({ required: true, type: String })
  name: string;

  @Prop({ type: [{ type: Types.ObjectId, ref: 'Invite' }] })
  invites: Types.ObjectId[];

  @Prop({
    type: String,
  })
  photo?: string;

  @Prop({ type: [{ type: Types.ObjectId, ref: 'CV' }] })
  cvs: Types.ObjectId[];

  @Prop({
    type: [
      {
        profile: {
          email: String,
          name: String,
          image: String,
          title: String,
          about: String,
          department: String,
          location: String,
          links: [String],
          references: [String],
          tags: [String],
          workExperience: [
            {
              companyName: { type: String, required: true },
              jobTitle: String,
              description: String,
              startDate: { type: Date, required: true },
              endDate: Date,
              currentlyWorkHere: Boolean,
            },
          ],
          education: [
            {
              schoolName: { type: String, required: true },
              degree: String,
              description: String,
              startDate: Date,
              endDate: Date,
            },
          ],
        },
        skills: [
          new mongoose.Schema({
            name: { type: String, required: true },
            isValidated: Boolean,
            validated: Date,
            level: Number,
            added: Date,
            validatedLevel: Number,
            type: String,
          }),
        ],
        certifications: [
          new mongoose.Schema({
            name: { type: String, required: true },
            issuingOrganization: { type: String, required: true },
            notExpiring: Boolean,
            issuedAt: Date,
            expiredAt: Date,
            added: Date,
            license: String,
            url: String,
          }),
        ],
      },
    ],
  })
  muchskillsMembers?: IMuchskillsMember[];

  @Prop({ type: [String] })
  locations: string[];

  @Prop({ type: String })
  description: string;

  @Prop({ type: String })
  website: string;

  @Prop({ type: String })
  timezone: string;

  //Integrations
  @Prop({
    type: {
      token: { type: String, select: false },
      connected: Boolean,
      lastSync: Date,
    },
  })
  muchskillsIntegration?: IMuchskillsIntegration;

  // Stripe related fields
  @Prop({ type: String, unique: true, sparse: true })
  stripeCustomerId?: string;

  @Prop({ type: String, unique: true, sparse: true })
  stripeSubscriptionId?: string;

  @Prop({ type: String, default: 'free' })
  planStatus?:
    | 'trialing'
    | 'active'
    | 'past_due'
    | 'canceled'
    | 'unpaid'
    | 'incomplete'
    | 'incomplete_expired'
    | 'paused'
    | 'free';

  @Prop({ type: Date })
  currentPeriodEnds?: Date;

  @Prop({ type: String })
  planId?: string;

  @Prop({
    type: String,
    enum: StripePlanMapping,
    default: StripePlanMapping.FREE,
  })
  planTier?: StripePlanMapping;
}

export const OrganizationSchema = SchemaFactory.createForClass(Organization);
