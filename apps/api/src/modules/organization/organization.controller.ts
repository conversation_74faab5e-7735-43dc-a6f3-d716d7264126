import {
  Controller,
  Delete,
  Get,
  Post,
  Param,
  UseGuards,
} from '@nestjs/common';

import { OrganizationService } from './organization.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { AuthUser } from '../global/decorators/user.decorator';
import { AuthUserDto } from '../global/dto/auth-user.dto';

@Controller('organization')
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @Get()
  @UseGuards(AuthGuard)
  async getOrganization(@AuthUser() user: AuthUserDto) {
    const { _id: orgId } = user.organization;

    return await this.organizationService.findById(orgId);
  }

  @Get('users')
  @UseGuards(AuthGuard)
  getOrganizationUsers(@AuthUser() user: AuthUserDto) {
    return this.organizationService.getOrganizationUsers(user.organization._id);
  }

  @Delete()
  @UseGuards(AuthGuard)
  deleteOrganization(@AuthUser() user: AuthUserDto) {
    return this.organizationService.deleteOrganization(user.organization._id);
  }

  @Post('set-active/:organizationId')
  @UseGuards(AuthGuard)
  setActiveOrganization(
    @AuthUser() user: AuthUserDto,
    @Param('organizationId') organizationId: string,
  ) {
    return this.organizationService.setActiveOrganization(
      user._id,
      organizationId,
    );
  }

  @Get('all')
  @UseGuards(AuthGuard)
  getAllOrganizations(@AuthUser() user: AuthUserDto) {
    return this.organizationService.getUserOrganizations(user._id);
  }
}
