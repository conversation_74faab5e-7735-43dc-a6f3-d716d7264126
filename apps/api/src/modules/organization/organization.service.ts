import {
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common'; // Added Logger, NotFoundException
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';

import {
  Organization,
  OrganizationDocument,
  IOrganization,
} from './organization.schema'; // Added IOrganization
import { CvsService } from '../cvs/cvs.service';
import { InviteService } from '../invite/invite.service';
import { User } from '../users/user.schema';
import { UsersService } from '../users/users.service';

@Injectable()
export class OrganizationService {
  private readonly logger = new Logger(OrganizationService.name); // Added logger

  constructor(
    @InjectModel(Organization.name)
    private organizationModel: Model<OrganizationDocument>,
    @Inject(forwardRef(() => InviteService))
    private readonly inviteService: InviteService,
    @Inject(forwardRef(() => UsersService))
    public readonly usersService: UsersService,
    @Inject(forwardRef(() => CvsService))
    private readonly cvService: CvsService, // private readonly s3Provider: S3Provider
  ) {}

  async createWithInvite({ name }: { name: string }): Promise<Organization> {
    const organization = await new this.organizationModel({
      name,
    }).save();

    return organization;
  }

  async findById(
    id: string | Types.ObjectId,
  ): Promise<OrganizationDocument | null> {
    return this.organizationModel.findById(id).exec();
  }

  async update(
    id: string | Types.ObjectId,
    updateData: Partial<IOrganization>,
  ): Promise<OrganizationDocument | null> {
    const organization = await this.organizationModel
      .findByIdAndUpdate(id, { $set: updateData }, { new: true })
      .exec();
    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found.`);
    }
    return organization;
  }

  async getOrganizationUsers(
    organizationId: string | Types.ObjectId,
  ): Promise<User[]> {
    return this.usersService.getOrganizationUsers(organizationId);
  }

  async deleteOrganization(organizationId: string | Types.ObjectId) {
    await this.organizationModel.findByIdAndDelete(organizationId);
    await this.inviteService.deleteOrganizationInvites(organizationId);
    // await this.usersService.deleteOrganizationUsers(organizationId);
    // delete cvs
    await this.cvService.deleteOrganizationCvs(organizationId);
  }

  async updateMuchskillsAuthToken(
    orgId: string | Types.ObjectId,
    authToken: string,
  ) {
    return this.organizationModel
      .findByIdAndUpdate(
        orgId,
        {
          $set: {
            'muchskillsIntegration.token': authToken,
            'muchskillsIntegration.connected': true,
          },
        },
        { new: true },
      ) // Added {new: true}
      .exec();
  }

  async getMuchskillsAuthToken(
    orgId: string | Types.ObjectId,
  ): Promise<string | null> {
    const organization = await this.organizationModel
      .findById(orgId)
      .select('+muchskillsIntegration.token')
      .exec();

    return organization?.muchskillsIntegration?.token || null;
  }

  // TEST('vladyslav'): test upload logo
  // TODO('vladyslav'): add upload logo, fix multer
  // async uploadLogo(user: AuthUserDto, img?: Express.Multer.File) {
  //   let url: string;
  //   if (img) {
  //     const sendData = await this.s3Provider.save(img.buffer, img.originalname);
  //     url = sendData.Location;
  //   }
  //   await this.organizationModel.findOneAndUpdate(
  //     { _id: user.organization._id },
  //     { $set: { photo: url } }
  //   );
  //   return { url };
  // }

  async setActiveOrganization(
    userId: string | Types.ObjectId,
    organizationId: string | Types.ObjectId,
  ) {
    const result = await this.usersService.setActiveOrganization(
      userId,
      organizationId,
    );
    const organization = await this.findById(result.organization);
    return organization;
  }

  async getUserOrganizations(userId: string | Types.ObjectId) {
    return this.usersService.getUserOrganizations(userId);
  }

  async addUserToOrganization(
    organizationId: string | Types.ObjectId,
    userId: string | Types.ObjectId,
  ) {
    const result = await this.usersService.addUserToOrganization(
      organizationId,
      userId,
    );
    this.logger.debug('Adding user to organization', {
      // Changed to debug
      organizationId,
      userId,
      result,
    });
    return result;
  }

  // New methods for Stripe integration
  async findByStripeCustomerId(
    stripeCustomerId: string,
  ): Promise<OrganizationDocument | null> {
    return this.organizationModel.findOne({ stripeCustomerId }).exec();
  }

  async updateByStripeCustomerId(
    stripeCustomerId: string,
    updateData: Partial<IOrganization>,
  ): Promise<OrganizationDocument | null> {
    const organization = await this.organizationModel
      .findOneAndUpdate(
        { stripeCustomerId },
        { $set: updateData },
        { new: true }, // Return the updated document
      )
      .exec();

    if (!organization) {
      this.logger.warn(
        `Organization not found with Stripe Customer ID: ${stripeCustomerId} for update.`,
      );
      // Do not throw NotFoundException here as Stripe webhooks might try to update
      // a customer that was created but the initial DB save failed or was delayed.
      // The webhook handler should log this and Stripe will retry.
    } else {
      this.logger.log(
        `Organization ${organization._id} updated via Stripe Customer ID ${stripeCustomerId}`,
      );
    }
    return organization;
  }
}
