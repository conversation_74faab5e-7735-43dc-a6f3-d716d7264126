import { Injectable, ForbiddenException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { StripePlanMapping } from 'shared/types';

import { Plan, PlanDocument } from './plan.schema';
import { ICv, CvModel } from '../cvs/cv.schema';
import { AuthUserDto } from '../global/dto/auth-user.dto'; // Assuming this DTO is still relevant for user context
import { Member, MemberDocument } from '../members/member.schema';
import { OrganizationDocument } from '../organization/organization.schema'; // Correct import path for OrganizationDocument
import { OrganizationService } from '../organization/organization.service';

// TODO: Define a more specific DTO if needed for checkCvLimit instead of full AuthUserDto
// TODO: Consider if error messages should be more generic or configurable

@Injectable()
export class PlanLimitsService {
  private readonly logger = new Logger(PlanLimitsService.name);

  constructor(
    // TODO: Check if forwardRef is needed for OrganizationService if circular dependencies arise with OrganizationModule
    private readonly organizationService: OrganizationService,
    @InjectModel(Plan.name) private planModel: Model<PlanDocument>,
    @InjectModel(CvModel.modelName) private cvModel: Model<ICv>,
    @InjectModel(Member.name) private memberModel: Model<MemberDocument>,
  ) {}

  private async getOrganizationAndPlan(
    user: AuthUserDto,
    context: string, // For logging context e.g., 'checkCvLimit'
  ): Promise<{ organization: OrganizationDocument; plan: PlanDocument }> {
    if (!user || !user.organization || !user.organization._id) {
      this.logger.warn(
        `User or organization ID not found in request for PlanLimitsService (${context})`,
      );
      throw new ForbiddenException(
        'User or organization information is missing.',
      );
    }

    const organizationId = user.organization._id.toString();
    const organization =
      await this.organizationService.findById(organizationId);

    if (!organization) {
      this.logger.warn(
        `Organization not found: ${organizationId} in PlanLimitsService (${context})`,
      );
      throw new ForbiddenException('Organization not found.');
    }

    const currentPlanTier = organization.planTier || StripePlanMapping.FREE;
    const plan = await this.planModel
      .findOne({ stripeMappingId: currentPlanTier })
      .exec();

    if (!plan) {
      this.logger.error(
        `Plan not found for stripeMappingId: ${currentPlanTier} in PlanLimitsService (${context}) for organization ${organizationId}`,
      );
      throw new ForbiddenException(
        `Your current plan details ('${currentPlanTier}') could not be found. Please contact support.`,
      );
    }
    return { organization, plan };
  }

  /**
   * Checks if an organization can create a new CV based on their current plan.
   * Throws a ForbiddenException if the limit is reached.
   * @param user - The authenticated user DTO containing organization info.
   * @returns Promise<void>
   */
  async checkCvLimit(user: AuthUserDto): Promise<void> {
    const { organization, plan } = await this.getOrganizationAndPlan(
      user,
      'checkCvLimit',
    );

    const maxCVs = plan.maxCVs;

    // If maxCVs is Infinity, no limit check is needed for CVs
    if (maxCVs === Infinity) {
      return; // No limit, so allow action
    }

    if (plan.stripeMappingId === StripePlanMapping.FREE) {
      // For FREE plan, check total CVs
      const totalCvs = await this.countTotalCvsByOrganization(
        organization._id as Types.ObjectId,
      );
      if (totalCvs >= maxCVs) {
        this.logger.error(
          `Total CV limit reached for FREE plan. Organization: ${organization._id}, Plan: ${plan.name}, Limit: ${maxCVs}, Total CVs: ${totalCvs}`,
        );
        throw new ForbiddenException(
          `You have reached the total limit of ${maxCVs} CVs for the Free plan. Please upgrade your plan to create more CVs.`,
        );
      }
    } else {
      // For other plans, check CVs created this month
      const cvsCreatedThisMonth =
        await this.countCvsCreatedInCurrentMonthByOrganization(
          organization._id as Types.ObjectId,
        );

      if (cvsCreatedThisMonth >= maxCVs) {
        this.logger.error(
          `Monthly CV limit reached for organization ${organization._id}. Plan: ${plan.name}, Limit: ${maxCVs}, Created this month: ${cvsCreatedThisMonth}`,
        );
        throw new ForbiddenException(
          `You have reached the monthly limit of ${maxCVs} CV creations for your current plan. Please upgrade your plan to create more CVs.`,
        );
      }
    }
  }

  private async countTotalCvsByOrganization(
    organizationId: Types.ObjectId,
  ): Promise<number> {
    return await this.cvModel.countDocuments({
      organization: organizationId,
    });
  }

  private async countCvsCreatedInCurrentMonthByOrganization(
    organizationId: Types.ObjectId,
  ): Promise<number> {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(
      now.getFullYear(),
      now.getMonth() + 1,
      0,
      23,
      59,
      59,
      999,
    );
    // TODO: Ensure timezone consistency if this becomes an issue across different server/DB settings

    return await this.cvModel.countDocuments({
      organization: organizationId,
      createdAt: {
        $gte: startOfMonth,
        $lte: endOfMonth,
      },
    });
  }

  async checkProfilesLimit(user: AuthUserDto): Promise<void> {
    const { organization, plan } = await this.getOrganizationAndPlan(
      user,
      'checkProfilesLimit',
    );

    const maxProfiles = plan.profiles;

    if (maxProfiles === Infinity) {
      return; // No limit
    }

    const currentProfileCount = await this.countMembersByOrganization(
      organization._id as Types.ObjectId,
    );

    if (currentProfileCount >= maxProfiles) {
      this.logger.error(
        `Profile limit reached for organization ${organization._id}. Plan: ${plan.name}, Limit: ${maxProfiles}, Current: ${currentProfileCount}`,
      );
      throw new ForbiddenException(
        `You have reached the limit of ${maxProfiles} profiles for your current plan. Please upgrade your plan to add more profiles.`,
      );
    }
  }

  private async countMembersByOrganization(
    organizationId: Types.ObjectId,
  ): Promise<number> {
    return await this.memberModel.countDocuments({
      organization: organizationId,
    });
  }
}
