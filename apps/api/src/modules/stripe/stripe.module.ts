import { Module, Global } from '@nestjs/common'; // Added forwardRef
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

import { PlanLimitGuard } from './guards/plan-limit.guard'; // Uncommented and ensure it's used
import { PlanLimitsService } from './plan-limits.service'; // Import the new service
import { Plan, PlanSchema } from './plan.schema';
import { StripeWebhookService } from './stripe-webhook.service';
import { StripeController } from './stripe.controller';
import { StripeService } from './stripe.service';
import { CvModel, CvSchema } from '../cvs/cv.schema';
import { Member, MemberSchema } from '../members/member.schema'; // Import Member schema
import { OrganizationModule } from '../organization/organization.module';
import { UsersModule } from '../users/users.module';
// TODO: If CvsModule exists and provides CvModel, import CvsModule instead of MongooseModule.forFeature for CvModel directly.
// For now, keeping direct MongooseModule.forFeature as per original structure.

@Global()
@Module({
  imports: [
    ConfigModule,
    OrganizationModule,
    UsersModule, // Assuming UsersModule is needed for other parts of Stripe functionality
    MongooseModule.forFeature([{ name: Plan.name, schema: PlanSchema }]),
    MongooseModule.forFeature([{ name: CvModel.modelName, schema: CvSchema }]),
    MongooseModule.forFeature([{ name: Member.name, schema: MemberSchema }]), // Add Member model
  ],
  providers: [
    StripeService,
    StripeWebhookService,
    PlanLimitsService, // Add the new service
    PlanLimitGuard, // Add the guard as a provider
  ],
  controllers: [StripeController],
  exports: [
    StripeService,
    StripeWebhookService,
    PlanLimitsService, // Export if other modules need to inject it directly
    // PlanLimitGuard // Guards are typically not exported unless used by other modules' controllers/resolvers directly
  ],
})
export class StripeModule {}
