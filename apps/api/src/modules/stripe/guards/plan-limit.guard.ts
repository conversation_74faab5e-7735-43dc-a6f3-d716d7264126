import {
  Injectable,
  CanActivate,
  ExecutionContext,
  // ForbiddenException, // No longer thrown directly by the guard
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core'; // Import Reflector

import { AuthUserDto } from '../../global/dto/auth-user.dto';
import { PlanLimitsService } from '../plan-limits.service';

export const LIMIT_TYPE_KEY = 'limit_type';
export enum LimitType {
  CVS = 'cvs',
  PROFILES = 'profiles',
  // Add other limit types here if needed in the future
}

@Injectable()
export class PlanLimitGuard implements CanActivate {
  private readonly logger = new Logger(PlanLimitGuard.name);

  constructor(
    private readonly planLimitsService: PlanLimitsService,
    private readonly reflector: Reflector, // Inject Reflector
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user as AuthUserDto;

    // The PlanLimitsService methods already perform user/organization validation.
    // If an AuthGuard runs before this, that's the primary place for user authentication.

    const limitType = this.reflector.get<LimitType>(
      LIMIT_TYPE_KEY,
      context.getHandler(),
    );

    if (!limitType) {
      // If the decorator is not used, but the guard is, what should happen?
      // For now, let's assume if the guard is used, the decorator should also be present.
      // Or, this guard could be designed to always check all limits if no specific type is provided,
      // but the request is to check based on controller.
      this.logger.warn(
        `PlanLimitGuard used without specifying LimitType on handler: ${context.getClass().name}.${context.getHandler().name}`,
      );
      // Depending on policy, you might deny access or log and allow (if other checks are in place)
      // For safety, let's deny if misconfigured.
      return false; // Or throw an error indicating misconfiguration
    }

    switch (limitType) {
      case LimitType.CVS:
        await this.planLimitsService.checkCvLimit(user);
        break;
      case LimitType.PROFILES:
        await this.planLimitsService.checkProfilesLimit(user);
        break;
      default:
        this.logger.warn(
          `Unknown LimitType specified: ${limitType} on handler: ${context.getClass().name}.${context.getHandler().name}`,
        );
        return false; // Deny access for unknown limit types
    }

    // If the service method does not throw an exception, the activation is allowed.
    return true;
  }

  // The countCvsCreatedInCurrentMonthByOrganization method and other logic
  // have been moved to PlanLimitsService.
}
