import { Currency, MemberSource, TimeRange, UserType } from './types';

export interface CostRateInput {
  currency: Currency;
  amount: number;
  timeRange: TimeRange;
}

export interface SocialInput {
  name: string;
  link: string;
}
export interface EducationRecordInput {
  hidden?: boolean;
  schoolName: string;
  degree?: string;
  description?: string;
  startDate?: string | Date;
  endDate?: string | Date;
}

export interface WorkHistoryRecordInput {
  hidden?: boolean;
  companyName: string;
  jobTitle?: string;
  description?: string;
  startDate: string | Date;
  endDate?: string | Date;
  currentlyWorkHere?: boolean;
}

export interface CreateMemberInput extends UpdateMemberInput {
  source: MemberSource;
}

export interface UpdateMemberInput {
  avatar?: string;
  avatarFile?: undefined;
  avatarPreview?: undefined;
  firstName: string;
  lastName?: string;
  email?: string;
  location?: string;
  telephone?: string;
  currentPosition?: string;
  currentLevel?: string;
  yearsOfExperience?: number;
  languages?: string[];
  type?: UserType;
  clients?: string[];
  costRate?: CostRateInput;
  costToCompany?: CostRateInput;
  socials?: SocialInput[];
  workExperience?: WorkHistoryRecordInput[];
  education?: EducationRecordInput[];
}
