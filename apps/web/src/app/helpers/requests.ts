import { AxiosError } from 'axios';
import { CreateCvDto } from 'shared/dto';
import { ResetPasswordDto } from 'shared/dto/auth/reset-password.dto';
import { SignInDto } from 'shared/dto/auth/sign-in.dto';
import { SignUpDto } from 'shared/dto/auth/sign-up.dto';
import { CreateMemberInput, UpdateMemberInput } from 'shared/inputs';
import {
  Paging,
  MemberSource,
  CvProfileFilter,
  PaginatedMuchskillsMembers,
  Cv,
  PaginatedMembers,
} from 'shared/types';

import api from '../api';

export const loginRequest = async (dto: SignInDto, token?: string) => {
  const response = await api.post('/login', dto, {
    params: token ? { token } : undefined,
  });

  if (response.data.organization) {
    localStorage.setItem(
      'organization',
      JSON.stringify({
        id: response.data.organization.id,
        name: response.data.organization.name,
        photo: response.data.organization.photo,
      }),
    );
  }

  return response.data;
};

export const getMembersRequest = async (paging: Paging) => {
  const response = await api.get('/members', {
    params: {
      page: paging.page,
      itemsPerPage: paging.itemsPerPage,
    },
  });

  return response.data as PaginatedMembers;
};

export const getMembersBySourceRequest = async (
  importType: MemberSource,
  paging: Paging,
) => {
  const response = await api.get('/members', {
    params: {
      source: importType,
      page: paging.page,
      itemsPerPage: paging.itemsPerPage,
    },
  });
  return response.data;
};

export const getMuchskillsMembers = async ({
  orgId,
  paging,
  searchValue,
  filter,
}: {
  orgId: string;
  paging: Paging;
  searchValue: string;
  filter: CvProfileFilter;
}) => {
  const response = await api.post(`/muchskills/members/${orgId}`, {
    page: paging.page,
    itemsPerPage: paging.itemsPerPage,
    searchValue,
    filter,
  });
  return response.data as PaginatedMuchskillsMembers;
};

export const syncMuchskillsMembers = async (orgId: string) => {
  await api.post(`/muchskills/members-sync/${orgId}`);
};

export const createMemberFromMuchskills = async ({
  orgId,
  email,
}: {
  orgId: string;
  email: string;
}) => {
  await api.post(`/muchskills/create-member/${orgId}/${email}`);
};

export const createMemberRequest = async (
  dto: CreateMemberInput,
  avatar?: File | null,
) => {
  try {
    const response = await api.post('/members/create', dto);
    const memberId = response.data._id;

    if (avatar && memberId) {
      const formData = new FormData();
      formData.append('avatar', avatar);

      await api.post(`/members/avatar-update/${memberId}`, formData);
    }
    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const updateMemberRequest = async (
  memberId: string,
  dto: UpdateMemberInput,
  avatar?: File | null,
) => {
  try {
    const response = await api.post(`/members/update/${memberId}`, dto);

    if (avatar !== undefined) {
      const formData = new FormData();

      if (avatar) {
        formData.append('avatar', avatar);
      } else {
        formData.append('removeAvatar', 'true');
      }

      await api.post(`/members/avatar-update/${memberId}`, formData);
    }

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const regenerateCvRequest = async (id: string, aiQuery: string) => {
  const response = await api.post(`/cvs/regenerate/${id}`, {
    query: aiQuery,
  });
  return response.data;
};

export const createCvRequest = async (memberId: string, dto: CreateCvDto) => {
  const response = await api.post(`/cvs/create/${memberId}`, dto);
  return response.data;
};

export const getCvsByMemberRequest = async (memberId: string) => {
  const response = await api.get(`/cvs/byMember/${memberId}`);
  return response.data as Cv[];
};

export const signUpRequest = async (dto: SignUpDto, token?: string) => {
  const response = await api.post('/sign-up', dto, {
    params: token ? { token } : undefined,
  });
  return response.data;
};

export const requestPasswordResetEmail = async (email: string) => {
  const response = await api.post('/reset-password-request', { email });
  return response.data;
};

export const resetPasswordRequest = async (dto: ResetPasswordDto) => {
  const response = await api.post('/reset-password', dto);
  return response.data;
};

export const logoutRequest = async () => {
  const response = await api.post('/sign-out');
  // Clear organization data from localStorage
  localStorage.removeItem('organization');
  return response.data;
};

export const connectToMuchskills = async (orgId: string, token: string) => {
  try {
    const response = await api.post(
      `/muchskills/connect/${orgId}`,
      {},
      {
        headers: {
          'ms-token': token,
        },
      },
    );

    return response.data;
  } catch (err) {
    const error = err as AxiosError<{ message: string; code?: string }>;
    if (error.response?.data) {
      throw error.response.data;
    }

    throw new Error(error.message);
  }
};

export const disconnectMuchskills = async (orgId: string) => {
  const response = await api.post(`/muchskills/disconnect/${orgId}`);
  return response.data;
};

export const getOrganizationUsersRequest = async () => {
  const response = await api.get('/organization/users');
  return response.data;
};

export const getOrganizationInvitesRequest = async () => {
  const response = await api.get('/invites');
  return response.data;
};

export const createOrganizationInviteRequest = async (dto: {
  email: string;
}) => {
  const response = await api.post('/invites', dto);
  return response.data;
};

export const deleteOrganizationRequest = async () => {
  const response = await api.delete('/organization');
  return response.data;
};

export const updateUserRequest = async (data: any) => {
  const response = await api.patch(`/members`);
  return response.data;
};

export const getAllOrganizationsRequest = async () => {
  const response = await api.get('/organization/all');
  return response.data;
};

export const setActiveOrganizationRequest = async (organizationId: string) => {
  const response = await api.post(`/organization/set-active/${organizationId}`);
  return response.data;
};

export const getOrganizationRequest = async () => {
  const response = await api.get('/organization');
  return response.data;
};

export const acceptInviteRequest = async (token: string) => {
  const response = await api.post('/invites/accept', { token });
  return response.data;
};

export const getInviteDetailsRequest = async (token: string) => {
  const response = await api.get(`/invites/details/${token}`);
  return response.data;
};

export const createStripePortalSessionRequest = async (
  dto?: Record<string, never>, // DTO is now empty, or can be omitted
) => {
  const response = await api.post('/stripe/create-portal-session', dto || {});
  return response.data as { url: string };
};

export const getStripeProductsRequest = async () => {
  const response = await api.get('/stripe/products');
  return response.data;
};
