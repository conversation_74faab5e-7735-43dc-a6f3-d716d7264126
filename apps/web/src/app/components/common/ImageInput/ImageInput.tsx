import { useRef, useState } from 'react';
import { MAX_AVATAR_SIZE } from 'shared/constants';
import { toast } from 'sonner';

import { cn } from '@/lib/utils';

interface ImageInputProps {
  url?: string | null;
  disabled?: boolean;
  onImageUpdate: (file: File | null, previewUrl: string | null) => void;
}

export function ImageInput({ url, disabled, onImageUpdate }: ImageInputProps) {
  const [errorText, setErrorText] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleUploadFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      setErrorText('Please select an image file');
      toast.error('Please select an image file');
      return;
    }

    if (file.size > MAX_AVATAR_SIZE) {
      setErrorText('Image must be smaller than 5MB');
      toast.error('Image must be smaller than 5MB');
      return;
    }

    // Create preview URL
    const reader = new FileReader();
    reader.onloadend = () => onImageUpdate(file, reader.result as string);
    reader.readAsDataURL(file);
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="flex items-center">
      <img
        src={url || '/images/person.png'}
        alt="preview"
        className="w-16 h-16 rounded-full object-cover mr-1"
      />
      <span
        className={cn(
          'text-smalldoge-3 font-bold text-msGray-2 mr-2 cursor-pointer',
          disabled && 'pointer-events-none text-msGray-4',
        )}
        onClick={triggerFileInput}
      >
        Upload
      </span>
      <span
        className={cn(
          'text-smalldoge-3 font-bold text-msRed-1 cursor-pointer',
          disabled && 'pointer-events-none text-msGray-4',
        )}
        onClick={() => onImageUpdate(null, null)}
      >
        Remove Image
      </span>

      {/* Hidden input */}
      <input
        type="file"
        accept="image/*"
        className="hidden"
        disabled={disabled}
        ref={fileInputRef}
        onChange={handleUploadFile}
      />
    </div>
  );
}
