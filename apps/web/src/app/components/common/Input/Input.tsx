import * as React from 'react';

import { cn } from '@/lib/utils';

const Input = React.forwardRef<
  HTMLInputElement,
  React.ComponentProps<'input'> & {
    prefixElement?: React.ReactElement;
    wrapperClassName?: string;
    error?: string;
  }
>(
  (
    { className, type, prefixElement, wrapperClassName, error, ...props },
    ref,
  ) => {
    return (
      <div className={cn(wrapperClassName, 'relative')}>
        {prefixElement}
        <input
          type={type}
          className={cn(
            'flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 shadow-sm transition-colors file:border-0 file:bg-transparent placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
            className,
            !!error && 'border-msRed-1',
          )}
          ref={ref}
          {...props}
        />
        {error && (
          <span className="text-smalldoge-4 text-msRed-1">{error}</span>
        )}
      </div>
    );
  },
);
Input.displayName = 'Input';

export { Input };
