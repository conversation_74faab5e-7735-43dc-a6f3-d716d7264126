import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

const buttonSecondaryVariants = cva(
  'h-6 w-fit px-2 border border-px rounded-[2px] text-smalldoge-4 transition-all disabled:opacity-50 disabled:pointer-events-none',
  {
    variants: {
      variant: {
        white:
          'bg-msWhite text-msBlack border-msBlack hover:bg-msGray-6 active:bg-msGray-5',
        green:
          'bg-msGreen-2 text-msWhite border border-msGreen-2 hover:bg-[#37A07A] active:bg-[#359B76] disabled:bg-msBlack disabled:border-msBlack',
        icon: 'border-msGray-5 h-fit p-0',
      },
      text: {
        normal: '',
        uppercase: 'uppercase',
      },
      padding: {
        iconLeft: 'pl-1',
        iconRight: 'pr-1',
      },
    },
    defaultVariants: {
      variant: 'white',
      text: 'uppercase',
    },
  },
);

export interface ButtonSecondaryProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonSecondaryVariants> {
  asChild?: boolean;
}

const ButtonSecondary = React.forwardRef<
  HTMLButtonElement,
  ButtonSecondaryProps
>(({ className, variant, text, padding, asChild = false, ...props }, ref) => {
  const Comp = asChild ? Slot : 'button';
  return (
    <Comp
      className={cn(
        buttonSecondaryVariants({ variant, text, padding, className }),
      )}
      ref={ref}
      {...props}
    />
  );
});
ButtonSecondary.displayName = 'ButtonSecondary';

export { ButtonSecondary, buttonSecondaryVariants };
