import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { formatDistanceToNow } from 'date-fns';
import { ChevronDown, Search } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  CvProfileFilter,
  cvProfileFilterData,
  Organization,
  Paging,
} from 'shared/types';
import { useDebouncedCallback } from 'use-debounce';

import { MembersList } from './MembersList';

import {
  ButtonSecondary,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Input,
  Loader,
  Pagination,
} from '@/components/common';
import { SETTINGS_PATH } from '@/helpers/constants';
import {
  getMuchskillsMembers,
  syncMuchskillsMembers,
} from '@/helpers/requests';

interface MuchskillsViewProps {
  organization: Organization;
}

export function MuchskillsView({ organization }: MuchskillsViewProps) {
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const [searchValue, setSearchValue] = useState<string>('');
  const [paging, setPaging] = useState<Paging>({
    page: 1,
    itemsPerPage: 12,
  });
  const [filter, setFilter] = useState<CvProfileFilter>(CvProfileFilter.all);

  const debounceSetSearchValue = useDebouncedCallback((val: string) => {
    setSearchValue(val);
    resetPaging();
  }, 500);

  const {
    data: muchskillsMembersData,
    isFetching: muchskillsMembersDataFetching,
    refetch: refetchMuchskillsMembersData,
  } = useQuery({
    queryKey: [
      'muchskillsMembers',
      {
        orgId: organization._id,
        paging,
        searchValue,
        filter,
      },
    ],
    queryFn: () =>
      getMuchskillsMembers({
        orgId: organization._id,
        paging,
        searchValue,
        filter,
      }),
    placeholderData: keepPreviousData,
  });

  const { mutate: syncMembers, isPending: syncIsPending } = useMutation({
    mutationFn: () => syncMuchskillsMembers(organization._id),
    onSuccess: () => {
      resetPaging();
      refetchMuchskillsMembersData();
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['organization'] });
      queryClient.invalidateQueries({ queryKey: ['paginatedMembers'] });
    },
  });

  const resetPaging = () => {
    setPaging({ ...paging, page: 1 });
  };

  if (!organization.muchskillsIntegration?.connected) {
    return (
      <div className="flex flex-col items-center justify-center border border-dashed border-s-msGray-4 rounded-[8px] px-2 py-10">
        <img
          className="mb-4"
          width={352}
          src="/images/muchskills-cvinventory.png"
          alt="no matches"
        />
        <span className="text-smalldoge-3 mb-12 max-w-[320px] text-center">
          Sync <b>MuchSkills</b> profiles to <b>CVInventory</b> for easier
          profile management and data sync
        </span>
        <ButtonSecondary onClick={() => navigate(SETTINGS_PATH.integrations)}>
          Set up API token
        </ButtonSecondary>
      </div>
    );
  }

  return (
    <>
      <div className="p-px mb-6">
        <Input
          prefixElement={
            <Search
              className="absolute transform -translate-y-2.5 translate-x-2 top-[50%]"
              size={20}
            />
          }
          placeholder="Search profile to create CV"
          className="h-12 pl-8"
          onChange={(e) => debounceSetSearchValue(e.target.value)}
        />
      </div>
      <div className="flex justify-between mb-2">
        <div className="flex space-x-1">
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center bg-msWhite border border-msGray-5 rounded-[100px] px-2 cursor-pointer">
                <span className="font-bold text-smalldoge-4">
                  Showing {cvProfileFilterData[filter].name}
                </span>
                <ChevronDown size={16} />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="start"
              className="w-36 prevent-drawer-outside-click"
            >
              <DropdownMenuGroup>
                {Object.values(CvProfileFilter).map((filter) => (
                  <DropdownMenuItem
                    key={filter}
                    onClick={() => {
                      setFilter(filter);
                      resetPaging();
                    }}
                  >
                    <span className="text-smalldoge-3">
                      {cvProfileFilterData[filter].name}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <Pagination
          page={paging.page}
          itemsPerPage={paging.itemsPerPage}
          total={muchskillsMembersData?.total}
          onItemsPerPageChange={(val) =>
            setPaging({ ...paging, itemsPerPage: val, page: 1 })
          }
          onPageChange={(page) => setPaging({ ...paging, page })}
        />
      </div>
      <div className="flex items-center mb-6 space-x-2">
        <ButtonSecondary onClick={() => syncMembers()} disabled={syncIsPending}>
          Sync now
        </ButtonSecondary>
        {syncIsPending ? (
          <Loader size={16} />
        ) : (
          organization.muchskillsIntegration.lastSync && (
            <span className="text-smalldoge-4">
              Last synced{' '}
              {formatDistanceToNow(
                organization.muchskillsIntegration?.lastSync,
              )}{' '}
              ago
            </span>
          )
        )}
      </div>
      <MembersList
        orgId={organization._id}
        membersFetching={muchskillsMembersDataFetching}
        noMembersFound={!muchskillsMembersData?.total}
        members={muchskillsMembersData?.members}
      />
    </>
  );
}
