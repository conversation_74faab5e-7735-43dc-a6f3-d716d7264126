import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

import { AboutMeData } from '../../types';

interface AboutMeFormProps {
  data: AboutMeData;
  onDataChange: (newData: AboutMeData) => void;
}

export function AboutMeForm({ data, onDataChange }: AboutMeFormProps) {
  return (
    <div className="flex flex-col space-y-5 pt-4 p-px">
      <ReactQuill
        modules={{
          toolbar: [
            ['bold', 'italic', 'underline'],
            [{ list: 'ordered' }, { list: 'bullet' }],
          ],
        }}
        theme="snow"
        value={data.aboutMe}
        onChange={(value, delta, source) => {
          if (source === 'user') {
            onDataChange({ ...data, aboutMe: value });
          }
        }}
      />
    </div>
  );
}
