import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

import { CustomSectionData } from '../../types';

interface PersonalInfoFormProps {
  data: CustomSectionData;
  onDataChange: (newData: CustomSectionData) => void;
}

export function CustomSection({ data, onDataChange }: PersonalInfoFormProps) {
  return (
    <div className="flex flex-col space-y-5 pt-4 p-px">
      <ReactQuill
        modules={{
          toolbar: [
            ['bold', 'italic', 'underline'],
            [{ list: 'ordered' }, { list: 'bullet' }],
          ],
        }}
        theme="snow"
        value={data.richText}
        onChange={(value, delta, source) => {
          if (source === 'user') {
            onDataChange({ ...data, richText: value });
          }
        }}
      />
    </div>
  );
}
