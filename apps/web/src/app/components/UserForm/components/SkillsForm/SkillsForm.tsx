import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

import { SkillsData } from '../../types';

interface SkillsFormProps {
  data: SkillsData;
  onDataChange: (newData: SkillsData) => void;
}

export function SkillsForm({ data, onDataChange }: SkillsFormProps) {
  return (
    <div className="flex flex-col space-y-5 pt-4 p-px">
      <ReactQuill
        modules={{
          toolbar: [
            ['bold', 'italic', 'underline'],
            [{ list: 'ordered' }, { list: 'bullet' }],
          ],
        }}
        theme="snow"
        value={data.skills}
        onChange={(value, delta, source) => {
          if (source === 'user') {
            onDataChange({ ...data, skills: value });
          }
        }}
      />
    </div>
  );
}
