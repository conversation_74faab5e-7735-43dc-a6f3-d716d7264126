import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

import { CertificationsData } from '../../types';

interface CertificationsFormProps {
  data: CertificationsData;
  onDataChange: (newData: CertificationsData) => void;
}

export function CertificationsForm({
  data,
  onDataChange,
}: CertificationsFormProps) {
  return (
    <div className="flex flex-col space-y-5 pt-4 p-px">
      <ReactQuill
        modules={{
          toolbar: [
            ['bold', 'italic', 'underline'],
            [{ list: 'ordered' }, { list: 'bullet' }],
          ],
        }}
        theme="snow"
        value={data.certifications}
        onChange={(value, delta, source) => {
          if (source === 'user') {
            onDataChange({ ...data, certifications: value });
          }
        }}
      />
    </div>
  );
}
