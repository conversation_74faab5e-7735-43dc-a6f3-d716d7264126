import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { DateRange } from 'react-day-picker';
import ReactQuill from 'react-quill';
import { CreateCvDto } from 'shared/dto';
import { Currency, currencyData, TimeRange, timeRangeData } from 'shared/types';

import { createCvRequest } from '../../helpers/requests';
import {
  ButtonPrimary,
  Checkbox,
  DateRangePicker,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Input,
} from '../common';

const orgLevels = [
  { id: 'l1', name: 'Level 1', color: '#278f24' },
  { id: 'l2', name: 'Level 2', color: '#4ECDC4' },
  { id: 'l3', name: 'Level 3', color: '#45B7D1' },
  { id: 'l4', name: 'Level 4', color: '#FFBE0B' },
  { id: 'l5', name: 'Level 5', color: '#FB5607' },
  { id: 'l6', name: 'Level 6', color: '#8338EC' },
  { id: 'l7', name: 'Level 7', color: '#3A86FF' },
  { id: 'l8', name: 'Level 8', color: '#06D6A0' },
  { id: 'l9', name: 'Level 9', color: '#FF006E' },
];

const orgRoles = [
  { id: 'softwareDev', name: 'Software Developer' },
  { id: 'designer', name: 'Designer' },
  { id: 'analyst', name: 'Analyst' },
];

const orgClients = [
  { id: 'volvo', name: 'Volvo' },
  { id: 'google', name: 'Google' },
  { id: 'cisco', name: 'Cisco' },
];

interface CvSettingsFormProps {
  memberId: string;
  onCancel: () => void;
}

export function CvSettingsForm({ memberId, onCancel }: CvSettingsFormProps) {
  const queryClient = useQueryClient();

  const [title, setTitle] = useState<string>('');
  const [maxPages, setMaxPages] = useState<number>();
  const [role, setRole] = useState<string>();
  const [level, setLevel] = useState<string>('l1');
  const [rate, setRate] = useState<number>();
  const [currency, setCurrency] = useState<Currency>(Currency.eur);
  const [timeRange, setTimeRange] = useState<TimeRange>(TimeRange.hour);
  const [client, setClient] = useState<string>();
  const [link, setLink] = useState<string>('');
  const [contractRange, setContractRange] = useState<DateRange>();
  const [autoRenewal, setAutoRenewal] = useState<boolean>(false);
  const [leastExperience, setLeastExperience] = useState<number>();
  const [maxExperience, setMaxExperience] = useState<number>();
  const [skills] = useState<string[]>(['Figma', 'Blender']);
  const [description, setDescription] = useState<string>('');

  const { mutate } = useMutation({
    mutationFn: () => {
      const dto: CreateCvDto = {
        title,
        maxPages,
        role,
        level,
        rate,
        currency,
        timeRange,
        client,
        link,
        contractStart: contractRange?.from,
        contractEnd: contractRange?.to,
        autoRenewal,
        leastExperience,
        maxExperience,
        skills,
        description,
      };
      return createCvRequest(memberId, dto);
    },
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ['memberCvs', { memberId }] }),
  });

  const selectedLevel = orgLevels.find((l) => l.id === level);
  const selectedRole = orgRoles.find((r) => r.id === role);
  const selectedClient = orgClients.find((c) => c.id === client);

  const handleResetFields = () => {
    setTitle('');
    setMaxPages(undefined);
    setLevel('l1');
    setRate(undefined);
    setCurrency(Currency.eur);
    setTimeRange(TimeRange.hour);
    setLink('');
    setContractRange(undefined);
    setAutoRenewal(false);
    setLeastExperience(undefined);
    setMaxExperience(undefined);
    setDescription('');
  };

  const handleCancel = () => {
    onCancel();
    handleResetFields();
  };

  const handleSave = () => {
    onCancel();
    mutate();
  };

  return (
    <div className="flex flex-col space-y-5">
      <span className="font-bold text-smalldoge-1">
        Let’s set some basic requirements for this CV
      </span>
      <div className="flex flex-col p-4 space-y-2">
        <div className="flex flex-col items-start space-y-1 sm:flex-row sm:items-center sm:space-x-1 sm:space-y-0">
          <div className="flex-shrink-0 w-40 font-bold text-smalldoge-4 text-msGray-3">
            Cv Alias
          </div>
          <Input
            value={title}
            className="text-smalldoge-3"
            wrapperClassName="flex-grow w-full"
            onChange={(e) => setTitle(e.target.value)}
          />
        </div>

        <div className="flex flex-col items-start space-y-1 sm:flex-row sm:items-center sm:space-x-1 sm:space-y-0">
          <div className="flex-shrink-0 w-40 font-bold text-smalldoge-4 text-msGray-3">
            Max Pages
          </div>
          <Input
            type="number"
            value={maxPages}
            className="text-smalldoge-3"
            wrapperClassName="flex-grow w-full"
            onChange={(e) => setMaxPages(+e.target.value)}
          />
        </div>

        <div className="flex flex-col items-start space-y-1 sm:flex-row sm:items-center sm:space-x-1 sm:space-y-0">
          <div className="flex-shrink-0 w-40 font-bold text-smalldoge-4 text-msGray-3">
            Connected Role
          </div>

          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center h-9">
                <div className="w-fit max-w-full bg-msGray-6 rounded-[100px] px-2">
                  <span className="font-bold truncate text-smalldoge-3">
                    {selectedRole ? selectedRole.name : 'Select role'}
                  </span>
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="start"
              className="overflow-auto prevent-drawer-outside-click max-h-48"
            >
              <DropdownMenuGroup>
                {orgRoles.map((role) => (
                  <DropdownMenuItem
                    key={role.id}
                    onClick={() => setRole(role.id)}
                  >
                    <span className="font-bold text-smalldoge-3">
                      {role.name}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex flex-col items-start space-y-1 sm:flex-row sm:items-center sm:space-x-1 sm:space-y-0">
          <div className="flex-shrink-0 w-40 font-bold text-smalldoge-4 text-msGray-3">
            Level Required
          </div>
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center h-9">
                <div className="flex items-center space-x-1 cursor-pointer">
                  <span
                    className="w-4 h-4 rounded-full"
                    style={{
                      backgroundColor: selectedLevel?.color || '#AFAFAF',
                    }}
                  />
                  <span className="font-bold text-smalldoge-3 text-msGray-2">
                    {selectedLevel ? selectedLevel.name : 'Select level'}
                  </span>
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="start"
              className="overflow-auto prevent-drawer-outside-click max-h-48"
            >
              <DropdownMenuGroup>
                {orgLevels.map((level) => (
                  <DropdownMenuItem
                    key={level.id}
                    onClick={() => setLevel(level.id)}
                  >
                    <div className="flex items-center space-x-1">
                      <span
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: level.color }}
                      />
                      <span className="font-bold text-smalldoge-3">
                        {level.name}
                      </span>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex flex-col items-start space-y-1 sm:flex-row sm:items-center sm:space-x-1 sm:space-y-0">
          <div className="flex-shrink-0 w-40 font-bold text-smalldoge-4 text-msGray-3">
            Contract Rate
          </div>
          <div className="flex items-center flex-grow w-full h-9">
            <div className="flex items-center space-x-2">
              <div className="relative rounded-sm bg-msGray-6">
                <span className="px-3 font-bold opacity-0 text-smalldoge-3 text-msGray-2">
                  {rate}
                </span>
                <div className="absolute top-0 left-0">
                  <Input
                    prefixElement={
                      <span className="text-smalldoge-3 text-msGray-2 font-bold absolute transform leading-5 -translate-y-2.5 translate-x-1.5 top-[50%]">
                        {currencyData[currency].sign}
                      </span>
                    }
                    className="w-full h-6 pl-4 pr-2 font-bold border-0 shadow-none outline-none text-smalldoge-3 text-msGray-2 focus-visible:ring-0"
                    value={rate}
                    onChange={(e) => setRate(+e.target.value)}
                  />
                </div>
              </div>
              <span>/</span>
              <DropdownMenu modal={false}>
                <DropdownMenuTrigger asChild>
                  <div className="flex items-center h-6 rounded-sm cursor-pointer bg-msGray-6">
                    <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                      {timeRangeData[timeRange].name.toUpperCase()}
                    </span>
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  collisionPadding={10}
                  align="start"
                  className="overflow-auto prevent-drawer-outside-click max-h-48"
                >
                  <DropdownMenuGroup>
                    {Object.values(TimeRange).map((range) => (
                      <DropdownMenuItem
                        key={range}
                        onClick={() => setTimeRange(range as TimeRange)}
                      >
                        <span className="font-bold truncate text-smalldoge-3">
                          {timeRangeData[range].name.toUpperCase()}
                        </span>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center h-6 ml-auto rounded-sm cursor-pointer bg-msGray-6">
                  <span className="px-2 font-bold text-smalldoge-3 text-msGray-2">
                    {currencyData[currency].name}
                  </span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                collisionPadding={10}
                align="start"
                className="overflow-auto prevent-drawer-outside-click max-h-48"
              >
                <DropdownMenuGroup>
                  {Object.values(Currency).map((curr) => (
                    <DropdownMenuItem
                      key={curr}
                      onClick={() => setCurrency(curr)}
                    >
                      <span className="font-bold truncate text-smalldoge-3">
                        {currencyData[curr].name}
                      </span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="flex flex-col items-start space-y-1 sm:flex-row sm:items-center sm:space-x-1 sm:space-y-0">
          <div className="flex-shrink-0 w-40 font-bold text-smalldoge-4 text-msGray-3">
            Client
          </div>
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center h-9">
                <div className="w-fit max-w-full bg-msGray-6 rounded-[100px] px-2">
                  <span className="font-bold truncate text-smalldoge-3">
                    {selectedClient ? selectedClient.name : 'Select client'}
                  </span>
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="start"
              className="overflow-auto prevent-drawer-outside-click max-h-48"
            >
              <DropdownMenuGroup>
                {orgClients.map((client) => (
                  <DropdownMenuItem
                    key={client.id}
                    onClick={() => setClient(client.id)}
                  >
                    <span className="font-bold text-smalldoge-3">
                      {client.name}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex flex-col items-start space-y-1 sm:flex-row sm:items-center sm:space-x-1 sm:space-y-0">
          <div className="flex-shrink-0 w-40 font-bold text-smalldoge-4 text-msGray-3">
            RFQ or Job Ad
          </div>
          <Input
            placeholder="URL goes here"
            value={link}
            className="text-smalldoge-3"
            wrapperClassName="flex-grow w-full"
            onChange={(e) => setLink(e.target.value)}
          />
        </div>

        <div className="flex flex-col items-start space-y-1 sm:flex-row sm:space-x-1 sm:space-y-0">
          <div className="flex-shrink-0 w-40 font-bold text-smalldoge-4 text-msGray-3">
            Contract Timeline
          </div>
          <div className="flex flex-col space-y-2">
            <DateRangePicker
              date={contractRange}
              onDateChange={setContractRange}
            />
            <div className="flex items-center space-x-2">
              <Checkbox
                id="autoRenew"
                checked={autoRenewal}
                onCheckedChange={(val) => setAutoRenewal(val as boolean)}
              />
              <label
                htmlFor="autoRenew"
                className="text-smalldoge-5 text-msGray-2"
              >
                Contract auto-renews
              </label>
            </div>
          </div>
        </div>

        <div className="flex flex-col items-start space-y-1 sm:flex-row sm:items-center sm:space-x-1 sm:space-y-0">
          <div className="flex-shrink-0 w-40 font-bold text-smalldoge-4 text-msGray-3">
            Experience Required
          </div>
          <div className="flex items-center space-x-2 h-9">
            <div className="relative rounded-sm bg-msGray-6">
              <div className="h-6 px-3 font-bold opacity-0 min-w-10 text-smalldoge-3 text-msGray-2">
                {leastExperience}
              </div>
              <div className="absolute top-0 left-0">
                <Input
                  type="number"
                  className="h-6 font-bold text-center border-0 shadow-none outline-none min-w-10 text-smalldoge-3 text-msGray-2 focus-visible:ring-0"
                  value={leastExperience}
                  onChange={(e) => setLeastExperience(+e.target.value)}
                />
              </div>
            </div>
            <span>-</span>
            <div className="relative rounded-sm bg-msGray-6">
              <div className="h-6 px-3 font-bold opacity-0 min-w-10 text-smalldoge-3 text-msGray-2">
                {maxExperience}
              </div>
              <div className="absolute top-0 left-0">
                <Input
                  type="number"
                  className="h-6 font-bold text-center border-0 shadow-none outline-none min-w-10 text-smalldoge-3 text-msGray-2 focus-visible:ring-0"
                  value={maxExperience}
                  onChange={(e) => setMaxExperience(+e.target.value)}
                />
              </div>
            </div>
            <span className="font-bold text-smalldoge-3 text-msGray-2">
              years
            </span>
          </div>
        </div>

        <div className="flex flex-col items-start space-y-1 sm:flex-row sm:items-center sm:space-x-1 sm:space-y-0">
          <div className="flex-shrink-0 w-40 font-bold text-smalldoge-4 text-msGray-3">
            Skills Required
          </div>
          <div className="flex items-center flex-grow w-full space-x-1 h-9">
            {skills.map((skill) => (
              <div
                key={skill}
                className="w-fit max-w-full bg-msGray-6 rounded-[100px] px-2"
              >
                <span className="font-bold truncate text-smalldoge-3">
                  {skill}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="flex flex-col items-start space-y-1 sm:flex-row sm:space-x-1 sm:space-y-0">
          <div className="flex-shrink-0 w-40 font-bold text-smalldoge-4 text-msGray-3">
            Description
          </div>
          <div className="w-full min-h">
            <ReactQuill
              modules={{
                toolbar: [
                  ['bold', 'italic', 'underline'],
                  [{ list: 'ordered' }, { list: 'bullet' }],
                ],
              }}
              theme="snow"
              value={description}
              onChange={(value, delta, source) => {
                if (source === 'user') {
                  setDescription(value);
                }
              }}
            />
          </div>
        </div>
      </div>
      <div className="flex justify-end mt-5 space-x-2">
        <ButtonPrimary variant={'white'} onClick={handleCancel}>
          Cancel
        </ButtonPrimary>
        <ButtonPrimary onClick={handleSave}>Save and Continue</ButtonPrimary>
      </div>
    </div>
  );
}
