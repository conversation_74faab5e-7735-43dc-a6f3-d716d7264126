import { useMemo } from 'react';
import { Skill } from 'shared/types';

import { Label } from '@/components/common';

interface SkillsFormProps {
  skills: Skill[];
}

export function SkillsForm({ skills }: SkillsFormProps) {
  const [techSkills, otherSkills] = useMemo(
    () =>
      skills.reduce(
        (res: Skill[][], skill) => {
          if (skill.type === 'tech') {
            res[0].push(skill);
          } else {
            res[1].push(skill);
          }

          return res;
        },
        [[], []],
      ),
    [skills],
  );

  if (!skills.length) {
    return (
      <div className="flex flex-col space-y-2 items-center justify-center border border-dashed border-s-msGray-4 rounded-[8px] px-2 py-6">
        <img src="/images/notes.svg" alt="notes" />
        <span className="text-smalldoge-3">There are no skills yet</span>
      </div>
    );
  }

  return (
    <div className="flex flex-col space-y-4">
      <div className="flex items-start flex-col space-y-2">
        <Label label="Technical Skills" />
        <div className="flex flex-wrap">
          {techSkills.map((skill, i) => (
            <div key={i} className="bg-msGray-6 rounded-[100px] px-2 mr-2 mb-1">
              <span className="font-bold truncate text-smalldoge-3">
                {skill.name}
              </span>
            </div>
          ))}
        </div>
      </div>
      <div className="flex items-start flex-col space-y-1">
        <Label label="Other Skills" />
        <div className="flex flex-wrap">
          {otherSkills.map((skill, i) => (
            <div key={i} className="bg-msGray-6 rounded-[100px] px-2 mr-2 mb-1">
              <span className="font-bold truncate text-smalldoge-3">
                {skill.name}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
