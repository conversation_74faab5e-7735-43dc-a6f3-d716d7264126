import { Certification } from 'shared/types';

import { Label } from '@/components/common';

interface CertificationsFormProps {
  certifications: Certification[];
}

export function CertificationsForm({
  certifications,
}: CertificationsFormProps) {
  if (!certifications.length) {
    return (
      <div className="flex flex-col space-y-2 items-center justify-center border border-dashed border-s-msGray-4 rounded-[8px] px-2 py-6">
        <img src="/images/notes.svg" alt="notes" />
        <span className="text-smalldoge-3">
          There are no certifications yet
        </span>
      </div>
    );
  }

  return (
    <div className="flex flex-col space-y-4">
      <div className="flex items-start flex-col space-y-2">
        <Label label="All certifications" />
        <div className="flex flex-wrap">
          {certifications.map((certification, i) => (
            <div key={i} className="bg-msGray-6 rounded-[100px] px-2 mr-2 mb-1">
              <span className="font-bold truncate text-smalldoge-3">
                {certification.name}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
