import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { User as User<PERSON>ogo, Trash2, UserPlus } from 'lucide-react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserRole } from 'shared/types';

import { ButtonSecondary } from '@/components/common';
import { ButtonDanger } from '@/components/common/ButtonDanger';
import { Drawer } from '@/components/common/Drawer';
import { Input } from '@/components/common/Input';
import { Label } from '@/components/common/Label';
import {
  getOrganizationUsersRequest,
  getOrganizationInvitesRequest,
  deleteOrganizationRequest,
  createOrganizationInviteRequest,
  updateUserRequest,
} from '@/helpers/requests';

interface User {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: UserRole;
}

interface Invite {
  _id: string;
  email: string;
  role: UserRole;
  status: 'PENDING' | 'ACCEPTED' | 'REJECTED';
}

export function OrganizationUsersPage() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isInviteDrawerOpen, setIsInviteDrawerOpen] = useState(false);
  const [isUserDetailsDrawerOpen, setIsUserDetailsDrawerOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [role, setRole] = useState<'OWNER' | 'ADMIN' | 'MEMBER'>('MEMBER');

  const { data: users } = useQuery({
    queryKey: ['organizationUsers'],
    queryFn: getOrganizationUsersRequest,
  });

  const { data: invites } = useQuery({
    queryKey: ['organizationInvites'],
    queryFn: getOrganizationInvitesRequest,
  });

  const { mutate: deleteOrganization } = useMutation({
    mutationFn: deleteOrganizationRequest,
    onSuccess: () => {
      navigate('/');
    },
  });

  const { mutate: createInvite } = useMutation({
    mutationFn: createOrganizationInviteRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organizationInvites'] });
      setIsInviteDrawerOpen(false);
      setInviteEmail('');
    },
  });

  const { mutate: updateUser } = useMutation({
    mutationFn: ({ userId, dto }: { userId: string; dto: Partial<User> }) =>
      updateUserRequest(userId, dto),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organizationUsers'] });
      setIsUserDetailsDrawerOpen(false);
      setSelectedUser(null);
    },
  });

  const handleUserClick = (user: User) => {
    setSelectedUser(user);
    setFirstName(user.firstName);
    setLastName(user.lastName);
    setRole(user.role);
    setIsUserDetailsDrawerOpen(true);
  };

  const handleSaveUser = () => {
    if (!selectedUser) return;
    updateUser({
      userId: selectedUser._id,
      dto: {
        firstName,
        lastName,
        role,
      },
    });
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-4 border-b border-msGray-5">
        <h1 className="text-xl font-semibold">Organization Users</h1>
        <div className="flex gap-2">
          <ButtonSecondary onClick={() => setIsInviteDrawerOpen(true)}>
            <UserPlus size={16} className="mr-2" />
            Invite User
          </ButtonSecondary>
          <ButtonDanger onClick={() => deleteOrganization()}>
            <Trash2 size={16} className="mr-2" />
            Delete Organization
          </ButtonDanger>
        </div>
      </div>

      <div className="flex-1 p-4 overflow-auto">
        <div className="space-y-4">
          <div>
            <h2 className="mb-2 text-lg font-medium">Users</h2>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-msGray-5">
                    <th className="p-2 text-left">Name</th>
                    <th className="p-2 text-left">Email</th>
                    <th className="p-2 text-left">Role</th>
                  </tr>
                </thead>
                <tbody>
                  {users && users.length > 0 ? (
                    users.map((user: User) => (
                      <tr
                        key={user._id}
                        className="border-b cursor-pointer border-msGray-5 hover:bg-msGray-6"
                        onClick={() => handleUserClick(user)}
                      >
                        <td className="p-2">
                          <div className="flex items-center">
                            <div className="flex items-center justify-center w-8 h-8 mr-2 rounded-full bg-msGray-4">
                              <UserLogo size={16} className="text-msGray-2" />
                            </div>
                            {user.firstName} {user.lastName}
                          </div>
                        </td>
                        <td className="p-2">{user.email}</td>
                        <td className="p-2">
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${
                              user.role === 'OWNER'
                                ? 'bg-msBlue-4 text-msBlue-1'
                                : user.role === 'ADMIN'
                                  ? 'bg-msGreen-4 text-msGreen-1'
                                  : 'bg-msGray-5 text-msGray-2'
                            }`}
                          >
                            {user.role}
                          </span>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={3} className="p-4 text-center text-msGray-3">
                        No organization users found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          <div>
            <h2 className="mb-2 text-lg font-medium">Pending Invites</h2>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-msGray-5">
                    <th className="p-2 text-left">Email</th>
                    <th className="p-2 text-left">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {invites && invites.length > 0 ? (
                    invites.map((invite: Invite) => (
                      <tr
                        key={invite._id}
                        className="border-b border-msGray-5 hover:bg-msGray-6"
                      >
                        <td className="p-2">
                          <div className="flex items-center">
                            <div className="flex items-center justify-center w-8 h-8 mr-2 rounded-full bg-msGray-4">
                              <UserLogo size={16} className="text-msGray-2" />
                            </div>
                            {invite.email}
                          </div>
                        </td>
                        <td className="p-2">
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${
                              invite.status === 'PENDING'
                                ? 'bg-msYellow-4 text-msYellow-1'
                                : invite.status === 'ACCEPTED'
                                  ? 'bg-msGreen-4 text-msGreen-1'
                                  : 'bg-msRed-4 text-msRed-1'
                            }`}
                          >
                            {invite.status}
                          </span>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={2} className="p-4 text-center text-msGray-3">
                        No pending invites
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <Drawer
        active={isInviteDrawerOpen}
        onClose={() => setIsInviteDrawerOpen(false)}
      >
        <div className="p-4 space-y-4">
          <div>
            <Label label="Email" labelProps={{ htmlFor: 'email' }} />
            <Input
              id="email"
              value={inviteEmail}
              onChange={(e) => setInviteEmail(e.target.value)}
              placeholder="Enter email address"
            />
          </div>
          <ButtonSecondary
            onClick={() =>
              createInvite({
                email: inviteEmail,
              })
            }
          >
            Send Invite
          </ButtonSecondary>
        </div>
      </Drawer>

      <Drawer
        active={isUserDetailsDrawerOpen}
        onClose={() => setIsUserDetailsDrawerOpen(false)}
      >
        <div className="p-4 space-y-4">
          <div>
            <Label label="First Name" labelProps={{ htmlFor: 'firstName' }} />
            <Input
              id="firstName"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              placeholder="Enter first name"
            />
          </div>
          <div>
            <Label label="Last Name" labelProps={{ htmlFor: 'lastName' }} />
            <Input
              id="lastName"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              placeholder="Enter last name"
            />
          </div>
          <div>
            <Label label="Role" labelProps={{ htmlFor: 'role' }} />
            <select
              id="role"
              value={role}
              onChange={(e) =>
                setRole(e.target.value as 'OWNER' | 'ADMIN' | 'MEMBER')
              }
              className="w-full p-2 border rounded-lg border-msGray-5 bg-msGray-6"
            >
              <option value="MEMBER">User</option>
              <option value="ADMIN">Admin</option>
              <option value="OWNER">Owner</option>
            </select>
          </div>
          <ButtonSecondary onClick={handleSaveUser}>
            Save Changes
          </ButtonSecondary>
        </div>
      </Drawer>
    </div>
  );
}
