import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { differenceInCalendarDays, format } from 'date-fns';
import {
  // AlignJustify,
  ArrowDownWideNarrow,
  ChevronDown,
  // LayoutGrid,
  RefreshCw,
  Search,
} from 'lucide-react';
import { useContext, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Paging,
  // currencyData,
  CvStatus,
  GroupFilter,
  groupFilterData,
  memberSourceData,
  // userTypeData,
} from 'shared/types';

import { getMembersRequest } from '../helpers/requests';

import {
  Avatar,
  ButtonSecondary,
  Drawer,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
  ImportForm,
  Input,
  Pagination,
  ProfileForm,
} from '@/components';
import { LayoutContext } from '@/components/Layout/Layout';
import { useOrganization } from '@/hooks/useOrganization';
import { cn } from '@/lib/utils';

enum SortOrder {
  name = 'name',
  cost = 'cost',
  freshness = 'freshness',
}

// enum View {
//   list = 'list',
//   grid = 'grid',
// }

const initialPaging = { page: 1, itemsPerPage: 12, total: 0 };

export function PeoplePage() {
  const navigate = useNavigate();

  const [sortOrder, setSortOrder] = useState<SortOrder>(SortOrder.name);
  // const [view, setView] = useState<View>(View.list);
  const [groupFilter, setGroupFilter] = useState<GroupFilter>(GroupFilter.all);
  const [paging, setPaging] = useState<Paging>(initialPaging);
  const [searchValue, setSearchValue] = useState<string>('');
  const [importDrawerActive, setImportDrawerActive] = useState<boolean>(false);
  const [editDrawerActive, setEditDrawerActive] = useState<boolean>(false);
  const [selectedProfile, setSelectedProfile] = useState<string | null>(null);

  const { setHeaderCallback } = useContext(LayoutContext);

  const { organization } = useOrganization();

  const { data: paginatedMembersData } = useQuery({
    queryKey: [
      'paginatedMembers',
      { orgId: 'myOrg', page: paging.page, itemsPerPage: paging.itemsPerPage },
    ],
    queryFn: async () => getMembersRequest(paging),
    placeholderData: keepPreviousData,
  });

  useEffect(() => {
    setHeaderCallback(`Profiles ${paginatedMembersData?.totalMembers}`);

    return () => setHeaderCallback('');
  }, [paginatedMembersData?.totalMembers, setHeaderCallback]);

  // const viewDropdown = useMemo(() => {
  //   const options = [
  //     {
  //       name: 'List',
  //       icon: <AlignJustify size={16} />,
  //       active: view === View.list,
  //       onClick: () => setView(View.list),
  //     },
  //     {
  //       name: 'Grid',
  //       icon: <LayoutGrid size={16} />,
  //       active: view === View.grid,
  //       onClick: () => setView(View.grid),
  //     },
  //   ];

  //   return (
  //     <DropdownMenu modal={false}>
  //       <DropdownMenuTrigger asChild>
  //         <div className="flex space-x-1 cursor-pointer">
  //           {options.find((o) => o.active)?.icon}
  //           <ChevronDown size={16} />
  //         </div>
  //       </DropdownMenuTrigger>
  //       <DropdownMenuContent collisionPadding={10} align="end" className="w-20">
  //         <DropdownMenuGroup>
  //           {options.map((option) => (
  //             <DropdownMenuItem key={option.name} onClick={option.onClick}>
  //               <span className="text-smalldoge-3">{option.name}</span>
  //             </DropdownMenuItem>
  //           ))}
  //         </DropdownMenuGroup>
  //       </DropdownMenuContent>
  //     </DropdownMenu>
  //   );
  // }, [view]);

  const sortDropdown = useMemo(() => {
    const options = [
      {
        name: 'Name',
        active: sortOrder === SortOrder.name,
        onClick: () => setSortOrder(SortOrder.name),
      },
      {
        name: 'Fresh',
        active: sortOrder === SortOrder.freshness,
        onClick: () => setSortOrder(SortOrder.freshness),
      },
      {
        name: 'Cost',
        active: sortOrder === SortOrder.cost,
        onClick: () => setSortOrder(SortOrder.cost),
      },
    ];

    return (
      <div className="flex items-center">
        <span className="hidden text-msGray-3 text-smalldoge-3 sm:block">
          Sort By
        </span>
        <ArrowDownWideNarrow className="block sm:hidden" size={16} />
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <span className="ml-1 cursor-pointer text-smalldoge-3 w-9">
              {options.find((o) => o.active)?.name}
            </span>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            collisionPadding={10}
            align="end"
            className="w-20"
          >
            <DropdownMenuGroup>
              {options.map((option) => (
                <DropdownMenuItem key={option.name} onClick={option.onClick}>
                  <span className="text-smalldoge-3">{option.name}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }, [sortOrder]);

  const groupFilterDropdown = useMemo(
    () => (
      <div className="flex space-x-1">
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild>
            <div className="flex items-center bg-msWhite border border-msGray-5 rounded-[100px] px-2 cursor-pointer">
              <span className="font-bold text-smalldoge-4">
                Showing {groupFilterData[groupFilter].name}
              </span>
              <ChevronDown size={16} />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            collisionPadding={10}
            align="end"
            className="w-20"
          >
            <DropdownMenuGroup>
              {Object.values(GroupFilter).map((group) => (
                <DropdownMenuItem
                  key={group}
                  onClick={() => setGroupFilter(group)}
                >
                  <span className="text-smalldoge-3">
                    {groupFilterData[group].name}
                  </span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    ),
    [groupFilter],
  );

  const profileToEdit = useMemo(() => {
    return paginatedMembersData?.members.find(
      (member) => member._id === selectedProfile,
    );
  }, [paginatedMembersData, selectedProfile]);

  return (
    <>
      <div className="flex flex-col h-full px-4 py-2">
        <div className="flex w-full mb-4">
          <span className="hidden font-black text-bigdoge-6 md:inline-block">
            Profiles {paginatedMembersData?.totalMembers}
          </span>
          <div className="flex items-center ml-auto space-x-6">
            {/* {viewDropdown} */}
            {sortDropdown}
            <div className="flex items-center space-x-1">
              <Search size={16} />
              <Input
                className="w-24 px-1 border-0 shadow-none outline-none focus-visible:ring-0 text-smalldoge-3 text-msGray-3 h-7"
                placeholder="Search"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
              />
            </div>
            <ButtonSecondary onClick={() => setImportDrawerActive(true)}>
              <span>New profile</span>
            </ButtonSecondary>
          </div>
        </div>
        <div className="flex justify-between mb-3">
          {groupFilterDropdown}
          <Pagination
            page={paging.page}
            itemsPerPage={paging.itemsPerPage}
            total={paginatedMembersData?.totalMembers}
            onItemsPerPageChange={(val) =>
              setPaging({ ...paging, itemsPerPage: val, page: 1 })
            }
            onPageChange={(page) => setPaging({ ...paging, page })}
          />
        </div>
        {/* TABLE */}
        <div className="w-full overflow-auto">
          <table className="table-fixed w-full min-w-[1100px] lg:min-w-full divide-y divide-msGray-5">
            <thead className="pb-1">
              <tr>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[11%]"
                >
                  Data from
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[16%]"
                >
                  Member
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[9%]"
                >
                  Cost rate
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[17%]"
                >
                  Contract renewal
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[15%]"
                >
                  Current assignment
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[14%]"
                >
                  Type
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[10%]"
                >
                  CV freshness
                </th>
                <th
                  scope="col"
                  className="text-smalldoge-5 text-msGray-3 text-left w-[8%]"
                >
                  CVs created
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-msGray-5">
              {paginatedMembersData?.members.map((member) => {
                const activeCVs = member.cvs.filter(
                  (cv) => cv.status === CvStatus.active,
                );

                return (
                  <tr key={member._id}>
                    <td className="py-2 pr-2">
                      <div className="relative flex w-fit">
                        <span
                          className={cn(
                            'w-10 h-10 rounded-full flex items-center justify-center mr-2',
                            memberSourceData[member.source].color,
                          )}
                        >
                          <img
                            width="20"
                            height="20"
                            src={memberSourceData[member.source].icon}
                            alt="source_logo"
                          />
                        </span>
                        <Avatar size={40} url={member.avatar} />
                        <span className="absolute w-5 h-5 flex justify-center items-center bg-msGreen-2 rounded-full text-msWhite transform -translate-x-2.5 -translate-y-2.5 left-[50%] top-[50%]">
                          <RefreshCw size={16} />
                        </span>
                      </div>
                    </td>
                    <td className="py-2 pr-2">
                      <div className="flex flex-col">
                        <DropdownMenu modal={false}>
                          <DropdownMenuTrigger asChild>
                            <div className="flex items-center cursor-pointer">
                              <span className="font-bold truncate text-smalldoge-3">
                                {`${member.firstName} ${member.lastName}`}
                              </span>
                              <ChevronDown
                                className="flex-shrink-0"
                                size={16}
                              />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent
                            collisionPadding={10}
                            align="end"
                            className="w-38"
                          >
                            <DropdownMenuGroup>
                              <DropdownMenuItem
                                onClick={() =>
                                  navigate(`/member/${member._id}`)
                                }
                              >
                                <span className="text-smalldoge-3">
                                  View CVs
                                </span>
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  setEditDrawerActive(true);
                                  setSelectedProfile(member._id);
                                }}
                              >
                                <span className="text-smalldoge-3">
                                  Edit base profile
                                </span>
                              </DropdownMenuItem>
                              {/* <DropdownMenuItem
                                onClick={() => {
                                  //Remove user
                                }}
                              >
                                <span className="text-smalldoge-3 text-msRed-1">
                                  Remove
                                </span>
                              </DropdownMenuItem> */}
                            </DropdownMenuGroup>
                          </DropdownMenuContent>
                        </DropdownMenu>

                        <span className="truncate text-smalldoge-5 text-msGray-2">
                          {member.currentPosition}
                        </span>
                        {/* <span className="truncate text-smalldoge-5 text-msGray-3">
                          {member.department}
                        </span> */}
                      </div>
                    </td>
                    <td className="py-2 pr-2">
                      <div className="flex flex-col space-y-2">
                        {/* {activeCVs.map((cv) => (
                          <div key={cv.id} className="flex items-center h-6">
                            <span className="text-smalldoge-5 text-msGray-2">
                              {currencyData[cv.currency].sign} {cv.costRate}
                            </span>
                          </div>
                        ))} */}
                      </div>
                    </td>
                    <td className="py-2 pr-2">
                      <div className="flex flex-col space-y-2">
                        {/* {activeCVs.map((cv) => (
                          <div key={cv.id} className="flex items-center h-6">
                            <span
                              className={cn(
                                'text-smalldoge-5 text-msGray-2',
                                !cv.autoRenewal && 'text-msPink-1'
                              )}
                            >
                              <b>{cv.autoRenewal ? 'Auto renewal' : 'Ends'}</b>{' '}
                              {format(cv.contractRenewal, 'PP')}
                            </span>
                          </div>
                        ))} */}
                      </div>
                    </td>
                    <td className="py-2 pr-2">
                      <div className="flex flex-col space-y-2">
                        {/* {activeCVs.map((cv) => (
                          <div key={cv.id} className="flex items-center h-6">
                            <div className="w-fit max-w-full flex bg-msGray-6 rounded-[100px] px-2">
                              <span className="truncate text-smalldoge-3 text-msBlue-1">
                                {cv.client}
                              </span>
                            </div>
                          </div>
                        ))} */}
                      </div>
                    </td>
                    <td className="py-2 pr-2">
                      <div className="flex flex-col space-y-2">
                        {/* {activeCVs.map((cv) => (
                          <div key={cv.id} className="flex items-center h-6">
                            <div
                              className={cn(
                                'w-fit max-w-full flex bg-msGray-6 rounded-[100px] px-2',
                                userTypeData[cv.userType].color
                              )}
                            >
                              <span className="truncate text-smalldoge-3">
                                {userTypeData[cv.userType].name}
                              </span>
                            </div>
                          </div>
                        ))} */}
                      </div>
                    </td>
                    <td className="py-2 pr-2">
                      <div className="flex flex-col space-y-2">
                        {activeCVs.map((cv) => {
                          const daysFromUpdate = differenceInCalendarDays(
                            new Date(),
                            cv.updatedAt,
                          );

                          return (
                            <div key={cv._id} className="flex items-center h-6">
                              <img
                                className="mr-2"
                                src={`/icons/${
                                  daysFromUpdate < 28
                                    ? 'TreeGreen'
                                    : daysFromUpdate < 56
                                      ? 'TreeYellow'
                                      : 'TreeDry'
                                }.svg`}
                                alt="tree"
                              />
                              <span className="mr-auto text-smalldoge-5 text-msGray-2">
                                {format(cv.updatedAt, 'PP')}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </td>
                    <td className="py-2 pr-2">
                      <ButtonSecondary
                        className="w-16"
                        onClick={() => navigate(`/member/${member._id}`)}
                      >
                        {member.cvs.length}
                      </ButtonSecondary>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
      {/* Profile Edit */}
      <Drawer
        active={editDrawerActive}
        onClose={() => setEditDrawerActive(false)}
      >
        {profileToEdit && (
          <div className="overflow-auto">
            <ProfileForm member={profileToEdit} />
          </div>
        )}
      </Drawer>
      {/* Import member */}
      <Drawer
        active={importDrawerActive}
        onClose={() => setImportDrawerActive(false)}
      >
        {organization && <ImportForm organization={organization} />}
      </Drawer>
    </>
  );
}
