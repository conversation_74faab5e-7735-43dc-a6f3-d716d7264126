import { Route, Routes, Navigate } from 'react-router-dom';

import AuthRoute from './components/AuthRoute/AuthRoute';
import Layout from './components/Layout/Layout';
import * as Pages from './pages';

import { NAVIGATE_PATH, SETTINGS_PATH } from '@/helpers/constants';

// Main routes component
export const AppRoutes = () => {
  return (
    <Routes>
      {/* Route for showing the accept invite page */}
      <Route
        path={NAVIGATE_PATH.acceptInvite}
        element={<Pages.AcceptInvitePage />}
      />

      {/* Auth routes */}
      <Route element={<AuthRoute />}>
        <Route path={NAVIGATE_PATH.login} element={<Pages.LoginPage />} />
        <Route path={NAVIGATE_PATH.signUp} element={<Pages.SignUpPage />} />
        <Route
          path={NAVIGATE_PATH.passwordReset}
          element={<Pages.PasswordResetPage />}
        />
        <Route
          path={NAVIGATE_PATH.setNewPassword}
          element={<Pages.SetNewPasswordPage />}
        />
        <Route
          path={NAVIGATE_PATH.emailVerification}
          element={<Pages.EmailVerificationPage />}
        />
      </Route>

      {/* Test route */}
      <Route
        path="/common-components-test"
        element={<Pages.CommonComponentsTestPage />}
      />

      {/* Protected routes */}
      <Route path="/" element={<Layout />}>
        {/* Default route - redirect to home */}
        <Route index element={<Navigate to={NAVIGATE_PATH.home} replace />} />
        <Route path={NAVIGATE_PATH.people} element={<Pages.PeoplePage />} />
        <Route
          path={`${NAVIGATE_PATH.member}/:memberId`}
          element={<Pages.MemberPage />}
        />
        <Route path={NAVIGATE_PATH.home} element={<Pages.ChatPage />} />
        {/* TODO(vladyslav): Move this under settings page after Eugen will add it */}
        <Route
          path={NAVIGATE_PATH.organizationUsers}
          element={<Pages.OrganizationUsersPage />}
        />
        <Route path={NAVIGATE_PATH.settings}>
          <Route
            path={SETTINGS_PATH.integrations}
            element={<Pages.IntegrationsPage />}
          />
          {/* Add Billing Page Route */}
          <Route
            path={SETTINGS_PATH.billing} // Use constant or fallback to string
            element={<Pages.BillingPage />}
          />
        </Route>
      </Route>
    </Routes>
  );
};
