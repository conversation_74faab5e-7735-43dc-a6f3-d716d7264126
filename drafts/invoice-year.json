{
  id: "in_1RYjHiPPczFzf6Wloov3fLee",
  object: "invoice",
  account_country: "SE",
  account_name: "CV-test",
  account_tax_ids: null,
  amount_due: 193300,
  amount_overpaid: 0,
  amount_paid: 193300,
  amount_remaining: 0,
  amount_shipping: 0,
  application: null,
  attempt_count: 0,
  attempted: true,
  auto_advance: false,
  automatic_tax: {
    disabled_reason: null,
    enabled: false,
    liability: null,
    provider: null,
    status: null,
  },
  automatically_finalizes_at: null,
  billing_reason: "subscription_create",
  collection_method: "charge_automatically",
  created: **********,
  currency: "eur",
  custom_fields: null,
  customer: "cus_STPlSwSxAZir81",
  customer_address: null,
  customer_email: "<EMAIL>",
  customer_name: "<EMAIL>",
  customer_phone: null,
  customer_shipping: null,
  customer_tax_exempt: "none",
  customer_tax_ids: [
  ],
  default_payment_method: null,
  default_source: null,
  default_tax_rates: [
  ],
  description: null,
  discounts: [
  ],
  due_date: null,
  effective_at: **********,
  ending_balance: 0,
  footer: `MuchSkills AB VAT number is SE559282252101 and organisation number is 559282-2521. 

You can either pay using payment link above or if you prefer to pay via bank transfer you can send payment to 

EURO PAYMENTS
Euro currency only - SWIFT/Bic TRWIBEB1XXX, Iban-number **************** and with bank address Wise Rue du Trône 100, 3rd floor Brussels 1050 Belgium

USD PAYMENTS 
USD currency  only - SWIFT/BIC CMFGUS33, ACH & Wire Routing Number *********, Account Number ********** and with bank address Community Federal Savings Bank, 89-16 Jamaica Ave, Woodhaven NY 11421
United States, New York NY 10010, United States

If your organisation is paying from Sweden you can pay via Bankgiro 5578-6669`,
  from_invoice: null,
  hosted_invoice_url: "https://invoice.stripe.com/i/acct_1RTQSzPPczFzf6Wl/test_YWNjdF8xUlRRU3pQUGN6RnpmNldsLF9TVGdmSDJEbjBSZzBTMlVxNlRxbmVZZ0FXbjJDUEx0LDE0MDE2ODQ4NA0200zBtZDfnT?s=ap",
  invoice_pdf: "https://pay.stripe.com/invoice/acct_1RTQSzPPczFzf6Wl/test_YWNjdF8xUlRRU3pQUGN6RnpmNldsLF9TVGdmSDJEbjBSZzBTMlVxNlRxbmVZZ0FXbjJDUEx0LDE0MDE2ODQ4NA0200zBtZDfnT/pdf?s=ap",
  issuer: {
    type: "self",
  },
  last_finalization_error: null,
  latest_revision: null,
  lines: {
    object: "list",
    data: [
      {
        id: "il_1RYjHhPPczFzf6WlgtaCyFKG",
        object: "line_item",
        amount: 193300,
        currency: "eur",
        description: "1 × CV Inventory Pro (at €1,933.00 / year)",
        discount_amounts: [
        ],
        discountable: true,
        discounts: [
        ],
        invoice: "in_1RYjHiPPczFzf6Wloov3fLee",
        livemode: false,
        metadata: {
          organizationId: "6846b4588ff4e74e43413c49",
        },
        parent: {
          invoice_item_details: null,
          subscription_item_details: {
            invoice_item: null,
            proration: false,
            proration_details: {
              credited_items: null,
            },
            subscription: "sub_1RYjHiPPczFzf6Wlv1yYVEwz",
            subscription_item: "si_STgfJ4CZbP0qHp",
          },
          type: "subscription_item_details",
        },
        period: {
          end: 1781163681,
          start: **********,
        },
        pretax_credit_amounts: [
        ],
        pricing: {
          price_details: {
            price: "price_1RVXuTPPczFzf6WlXGuQmLM6",
            product: "prod_SQOhbx86P1VD6Q",
          },
          type: "price_details",
          unit_amount_decimal: "193300",
        },
        quantity: 1,
        taxes: [
        ],
      },
    ],
    has_more: false,
    total_count: 1,
    url: "/v1/invoices/in_1RYjHiPPczFzf6Wloov3fLee/lines",
  },
  livemode: false,
  metadata: {
  },
  next_payment_attempt: null,
  number: "0PSALASL-0003",
  on_behalf_of: null,
  parent: {
    quote_details: null,
    subscription_details: {
      metadata: {
        organizationId: "6846b4588ff4e74e43413c49",
      },
      subscription: "sub_1RYjHiPPczFzf6Wlv1yYVEwz",
    },
    type: "subscription_details",
  },
  payment_settings: {
    default_mandate: null,
    payment_method_options: {
      acss_debit: null,
      bancontact: null,
      card: {
        request_three_d_secure: "automatic",
      },
      customer_balance: null,
      konbini: null,
      sepa_debit: null,
      us_bank_account: null,
    },
    payment_method_types: [
      "card",
    ],
  },
  period_end: **********,
  period_start: **********,
  post_payment_credit_notes_amount: 0,
  pre_payment_credit_notes_amount: 0,
  receipt_number: null,
  rendering: {
    amount_tax_display: "exclude_tax",
    pdf: null,
    template: null,
    template_version: null,
  },
  shipping_cost: null,
  shipping_details: null,
  starting_balance: 0,
  statement_descriptor: null,
  status: "paid",
  status_transitions: {
    finalized_at: **********,
    marked_uncollectible_at: null,
    paid_at: **********,
    voided_at: null,
  },
  subtotal: 193300,
  subtotal_excluding_tax: 193300,
  test_clock: null,
  total: 193300,
  total_discount_amounts: [
  ],
  total_excluding_tax: 193300,
  total_pretax_credit_amounts: [
  ],
  total_taxes: [
  ],
  webhooks_delivered_at: null,
}