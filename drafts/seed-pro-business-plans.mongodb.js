// MongoDB Shell Script to Seed Pro and Business Plans

// Select your database
// use your_database_name; // Replace 'your_database_name' with your actual database name

// The collection name is 'plans' (lowercase plural of the class name 'Plan')
const collectionName = 'plans';

// Data for the Pro Plan
const proPlanData = {
  name: 'Pro Plan',
  stripeMappingId: 'pro', // Corresponds to StripePlanMapping.PRO
  description: 'Ideal for small recruiting teams or boutiques.',
  profiles: 100,
  maxCVs: 50,
  users: 5,
  aiGeneratedCVs: true,
  cvDatabase: true,
  skillsIntegration: 'MuchSkills',
  skillsSearchAndMatching: true,
  dedicatedSupport: false, // Not mentioned
  apiIntegrations: false, // "Custom integrations: None"
  ssoSamlScim: false, // Not mentioned
  sla: false, // Not mentioned
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Data for the Business Plan
const businessPlanData = {
  name: 'Business Plan',
  stripeMappingId: 'business', // Corresponds to StripePlanMapping.BUSINESS
  description: 'Medium agencies, internal recruitment teams.',
  profiles: 500,
  maxCVs: 100,
  users: 20,
  aiGeneratedCVs: true,
  cvDatabase: true, // Implied from "Everything in Pro +"
  skillsIntegration: 'MuchSkills', // Implied from "Everything in Pro +"
  skillsSearchAndMatching: true, // "Yes (limited)" interpreted as true
  dedicatedSupport: false, // Not explicitly "Yes"
  apiIntegrations: true, // "Custom integrations: Possible"
  ssoSamlScim: false, // Not mentioned
  sla: false, // Not mentioned
  createdAt: new Date(),
  updatedAt: new Date(),
};

function seedPlan(planData) {
  const existingPlan = db
    .getCollection(collectionName)
    .findOne({ stripeMappingId: planData.stripeMappingId });

  if (existingPlan) {
    print(
      `Plan '${planData.name}' (stripeMappingId: ${planData.stripeMappingId}) already exists with _id: ${existingPlan._id}`,
    );
    // Optionally, update existing plan:
    // db.getCollection(collectionName).updateOne({ stripeMappingId: planData.stripeMappingId }, { $set: planData });
    // print(`Existing plan '${planData.name}' updated.`);
  } else {
    const result = db.getCollection(collectionName).insertOne(planData);
    if (result.insertedId) {
      print(
        `Successfully inserted plan '${planData.name}' with _id: ${result.insertedId}`,
      );
    } else {
      print(`Failed to insert plan '${planData.name}'.`);
      if (result.writeError) {
        print(`Write Error: ${result.writeError.errmsg}`);
      }
    }
  }
}

print('Starting to seed Pro and Business plans...');

seedPlan(proPlanData);
seedPlan(businessPlanData);

print('Script finished.');
