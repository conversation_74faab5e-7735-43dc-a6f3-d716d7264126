// MongoDB Shell Script to Seed Free Plan

// Select your database
// use your_database_name; // Replace 'your_database_name' with your actual database name

// The collection name is 'plans' (lowercase plural of the class name 'Plan')
const collectionName = 'plans';

// Data for the Free Plan
const freePlanData = {
  name: 'Free Plan',
  stripeMappingId: 'free', // Corresponds to StripePlanMapping.FREE
  description: 'Ideal for tryouts, small agencies',
  profiles: 20,
  maxCVs: 20,
  users: 5,
  aiGeneratedCVs: true, // "Yes (limited)" interpreted as true
  cvDatabase: true, // "Yes" interpreted as true
  skillsIntegration: 'MuchSkills',
  skillsSearchAndMatching: true, // "Yes (limited)" interpreted as true
  dedicatedSupport: false,
  apiIntegrations: false,
  ssoSamlScim: false,
  sla: false,
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Check if a free plan already exists to avoid duplicates
const existingFreePlan = db
  .getCollection(collectionName)
  .findOne({ stripeMappingId: 'free' });

if (existingFreePlan) {
  print('Free plan already exists with _id: ' + existingFreePlan._id);
  // Optionally, you could update it here if needed:
  // db.getCollection(collectionName).updateOne({ stripeMappingId: "free" }, { $set: freePlanData });
  // print("Existing Free plan updated.");
} else {
  // Insert the Free Plan document
  const result = db.getCollection(collectionName).insertOne(freePlanData);
  if (result.insertedId) {
    print('Successfully inserted Free Plan with _id: ' + result.insertedId);
  } else {
    print('Failed to insert Free Plan.');
    if (result.writeError) {
      print('Write Error: ' + result.writeError.errmsg);
    }
  }
}

print('Script finished.');
