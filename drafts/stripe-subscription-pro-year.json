{
  id: "sub_1RYjHiPPczFzf6Wlv1yYVEwz",
  object: "subscription",
  application: null,
  application_fee_percent: null,
  automatic_tax: {
    disabled_reason: null,
    enabled: false,
    liability: null,
  },
  billing_cycle_anchor: **********,
  billing_cycle_anchor_config: null,
  billing_thresholds: null,
  cancel_at: null,
  cancel_at_period_end: false,
  canceled_at: null,
  cancellation_details: {
    comment: null,
    feedback: null,
    reason: null,
  },
  collection_method: "charge_automatically",
  created: **********,
  currency: "eur",
  customer: "cus_STPlSwSxAZir81",
  days_until_due: null,
  default_payment_method: "pm_1RYjHaPPczFzf6WlaoKkO7Ls",
  default_source: null,
  default_tax_rates: [
  ],
  description: null,
  discounts: [
  ],
  ended_at: null,
  invoice_settings: {
    account_tax_ids: null,
    issuer: {
      type: "self",
    },
  },
  items: {
    object: "list",
    data: [
      {
        id: "si_STgfJ4CZbP0qHp",
        object: "subscription_item",
        billing_thresholds: null,
        created: **********,
        current_period_end: **********,
        current_period_start: **********,
        discounts: [
        ],
        metadata: {
        },
        plan: {
          id: "price_1RVXuTPPczFzf6WlXGuQmLM6",
          object: "plan",
          active: true,
          amount: 193300,
          amount_decimal: "193300",
          billing_scheme: "per_unit",
          created: **********,
          currency: "eur",
          interval: "year",
          interval_count: 1,
          livemode: false,
          metadata: {
          },
          meter: null,
          nickname: null,
          product: "prod_SQOhbx86P1VD6Q",
          tiers_mode: null,
          transform_usage: null,
          trial_period_days: null,
          usage_type: "licensed",
        },
        price: {
          id: "price_1RVXuTPPczFzf6WlXGuQmLM6",
          object: "price",
          active: true,
          billing_scheme: "per_unit",
          created: **********,
          currency: "eur",
          custom_unit_amount: null,
          livemode: false,
          lookup_key: null,
          metadata: {
          },
          nickname: null,
          product: "prod_SQOhbx86P1VD6Q",
          recurring: {
            interval: "year",
            interval_count: 1,
            meter: null,
            trial_period_days: null,
            usage_type: "licensed",
          },
          tax_behavior: "exclusive",
          tiers_mode: null,
          transform_quantity: null,
          type: "recurring",
          unit_amount: 193300,
          unit_amount_decimal: "193300",
        },
        quantity: 1,
        subscription: "sub_1RYjHiPPczFzf6Wlv1yYVEwz",
        tax_rates: [
        ],
      },
    ],
    has_more: false,
    total_count: 1,
    url: "/v1/subscription_items?subscription=sub_1RYjHiPPczFzf6Wlv1yYVEwz",
  },
  latest_invoice: "in_1RYjHiPPczFzf6Wloov3fLee",
  livemode: false,
  metadata: {
    organizationId: "6846b4588ff4e74e43413c49",
  },
  next_pending_invoice_item_invoice: null,
  on_behalf_of: null,
  pause_collection: null,
  payment_settings: {
    payment_method_options: {
      acss_debit: null,
      bancontact: null,
      card: {
        network: null,
        request_three_d_secure: "automatic",
      },
      customer_balance: null,
      konbini: null,
      sepa_debit: null,
      us_bank_account: null,
    },
    payment_method_types: [
      "card",
    ],
    save_default_payment_method: "off",
  },
  pending_invoice_item_interval: null,
  pending_setup_intent: null,
  pending_update: null,
  plan: {
    id: "price_1RVXuTPPczFzf6WlXGuQmLM6",
    object: "plan",
    active: true,
    amount: 193300,
    amount_decimal: "193300",
    billing_scheme: "per_unit",
    created: **********,
    currency: "eur",
    interval: "year",
    interval_count: 1,
    livemode: false,
    metadata: {
    },
    meter: null,
    nickname: null,
    product: "prod_SQOhbx86P1VD6Q",
    tiers_mode: null,
    transform_usage: null,
    trial_period_days: null,
    usage_type: "licensed",
  },
  quantity: 1,
  schedule: null,
  start_date: **********,
  status: "active",
  test_clock: null,
  transfer_data: null,
  trial_end: null,
  trial_settings: {
    end_behavior: {
      missing_payment_method: "create_invoice",
    },
  },
  trial_start: null,
}